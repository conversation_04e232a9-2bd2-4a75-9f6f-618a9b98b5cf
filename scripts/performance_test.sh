#!/bin/bash

# CMDB Scanner 性能测试脚本
# 用于测试优化前后的性能差异

set -e

# 配置参数
CONFIG_FILE="config2.yaml"
LOG_DIR="logs/performance_test"
RESULTS_DIR="performance_results"
TEST_DATE=$(date +%Y-%m-%d)

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 CMDB Scanner 性能测试脚本${NC}"
echo "=================================="

# 创建测试目录
mkdir -p "$LOG_DIR"
mkdir -p "$RESULTS_DIR"

# 函数：运行性能测试
run_performance_test() {
    local test_name=$1
    local config_file=$2
    local log_file="$LOG_DIR/${test_name}_${TEST_DATE}.log"
    local result_file="$RESULTS_DIR/${test_name}_${TEST_DATE}.json"
    
    echo -e "${BLUE}📊 开始性能测试: $test_name${NC}"
    echo "配置文件: $config_file"
    echo "日志文件: $log_file"
    echo "结果文件: $result_file"
    
    # 记录开始时间
    start_time=$(date +%s.%N)
    
    # 运行扫描程序
    if go run cmd/main.go -c "$config_file" > "$log_file" 2>&1; then
        # 记录结束时间
        end_time=$(date +%s.%N)
        duration=$(echo "$end_time - $start_time" | bc)
        
        echo -e "${GREEN}✅ 测试完成，耗时: ${duration}s${NC}"
        
        # 提取性能统计信息
        extract_performance_stats "$log_file" "$result_file" "$duration"
        
        return 0
    else
        echo -e "${RED}❌ 测试失败${NC}"
        echo "查看日志文件获取详细信息: $log_file"
        return 1
    fi
}

# 函数：提取性能统计信息
extract_performance_stats() {
    local log_file=$1
    local result_file=$2
    local total_duration=$3
    
    echo -e "${BLUE}📈 提取性能统计信息${NC}"
    
    # 从日志中提取关键性能指标
    local cluster_count=$(grep -o "获取到 [0-9]* 个cluster_id" "$log_file" | grep -o "[0-9]*" | head -1)
    local batch_count=$(grep -o "分为 [0-9]* 个批次" "$log_file" | grep -o "[0-9]*" | head -1)
    local result_count=$(grep -o "共获得 [0-9]* 个结果" "$log_file" | grep -o "[0-9]*" | head -1)
    
    # 提取各阶段耗时
    local preload_time=$(grep "数据预加载完成，耗时:" "$log_file" | grep -o "[0-9.]*[a-z]*" | head -1)
    local user_components_time=$(grep "用户组件并行处理完成，耗时:" "$log_file" | grep -o "[0-9.]*[a-z]*" | head -1)
    local meta_components_time=$(grep "Meta组件并行处理完成，耗时:" "$log_file" | grep -o "[0-9.]*[a-z]*" | head -1)
    
    # 创建性能报告JSON
    cat > "$result_file" << EOF
{
    "test_name": "$(basename "$result_file" .json)",
    "test_date": "$TEST_DATE",
    "total_duration": "$total_duration",
    "cluster_count": ${cluster_count:-0},
    "batch_count": ${batch_count:-0},
    "result_count": ${result_count:-0},
    "stage_timings": {
        "data_preload": "$preload_time",
        "user_components": "$user_components_time",
        "meta_components": "$meta_components_time"
    },
    "performance_metrics": {
        "clusters_per_second": $(echo "scale=2; ${cluster_count:-0} / $total_duration" | bc),
        "results_per_second": $(echo "scale=2; ${result_count:-0} / $total_duration" | bc)
    }
}
EOF
    
    echo -e "${GREEN}✅ 性能统计信息已保存到: $result_file${NC}"
}

# 函数：比较性能结果
compare_performance() {
    echo -e "${BLUE}📊 性能对比分析${NC}"
    echo "=================================="
    
    # 查找最新的测试结果文件
    local optimized_result=$(ls -t "$RESULTS_DIR"/optimized_*.json 2>/dev/null | head -1)
    local baseline_result=$(ls -t "$RESULTS_DIR"/baseline_*.json 2>/dev/null | head -1)
    
    if [[ -f "$optimized_result" && -f "$baseline_result" ]]; then
        echo "对比文件:"
        echo "  优化版本: $optimized_result"
        echo "  基准版本: $baseline_result"
        echo ""
        
        # 提取关键指标进行对比
        local opt_duration=$(jq -r '.total_duration' "$optimized_result")
        local base_duration=$(jq -r '.total_duration' "$baseline_result")
        
        local opt_clusters=$(jq -r '.cluster_count' "$optimized_result")
        local base_clusters=$(jq -r '.cluster_count' "$baseline_result")
        
        local opt_results=$(jq -r '.result_count' "$optimized_result")
        local base_results=$(jq -r '.result_count' "$baseline_result")
        
        # 计算性能提升
        local time_improvement=$(echo "scale=2; ($base_duration - $opt_duration) / $base_duration * 100" | bc)
        local throughput_improvement=$(echo "scale=2; ($opt_results / $opt_duration) / ($base_results / $base_duration) * 100 - 100" | bc)
        
        echo "性能对比结果:"
        echo "  总耗时:"
        echo "    基准版本: ${base_duration}s"
        echo "    优化版本: ${opt_duration}s"
        echo -e "    ${GREEN}时间节省: ${time_improvement}%${NC}"
        echo ""
        echo "  处理能力:"
        echo "    基准版本: $(echo "scale=2; $base_results / $base_duration" | bc) 结果/秒"
        echo "    优化版本: $(echo "scale=2; $opt_results / $opt_duration" | bc) 结果/秒"
        echo -e "    ${GREEN}吞吐量提升: ${throughput_improvement}%${NC}"
        echo ""
        
        # 生成对比报告
        local comparison_file="$RESULTS_DIR/performance_comparison_${TEST_DATE}.json"
        cat > "$comparison_file" << EOF
{
    "comparison_date": "$TEST_DATE",
    "baseline": $(cat "$baseline_result"),
    "optimized": $(cat "$optimized_result"),
    "improvements": {
        "time_saved_percent": $time_improvement,
        "throughput_improvement_percent": $throughput_improvement,
        "absolute_time_saved": $(echo "$base_duration - $opt_duration" | bc)
    }
}
EOF
        
        echo -e "${GREEN}✅ 性能对比报告已保存到: $comparison_file${NC}"
        
    else
        echo -e "${YELLOW}⚠️  未找到足够的测试结果文件进行对比${NC}"
        echo "请先运行基准测试和优化测试"
    fi
}

# 函数：显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -t, --test TYPE     运行指定类型的测试 (baseline|optimized|both)"
    echo "  -c, --config FILE   指定配置文件 (默认: $CONFIG_FILE)"
    echo "  -r, --compare       比较性能结果"
    echo "  -h, --help          显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -t both          # 运行基准和优化测试"
    echo "  $0 -r               # 比较性能结果"
    echo "  $0 -t optimized -c config.yaml  # 使用指定配置运行优化测试"
}

# 主执行逻辑
main() {
    local test_type="both"
    local compare_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--test)
                test_type="$2"
                shift 2
                ;;
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -r|--compare)
                compare_only=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo -e "${RED}未知参数: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查配置文件是否存在
    if [[ ! -f "$CONFIG_FILE" ]]; then
        echo -e "${RED}❌ 配置文件不存在: $CONFIG_FILE${NC}"
        exit 1
    fi
    
    # 检查依赖
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ 需要安装 Go${NC}"
        exit 1
    fi
    
    if ! command -v bc &> /dev/null; then
        echo -e "${RED}❌ 需要安装 bc 计算器${NC}"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        echo -e "${RED}❌ 需要安装 jq JSON处理器${NC}"
        exit 1
    fi
    
    # 执行测试
    if [[ "$compare_only" == true ]]; then
        compare_performance
    else
        case $test_type in
            baseline)
                echo -e "${YELLOW}注意: 基准测试需要使用未优化的代码版本${NC}"
                run_performance_test "baseline" "$CONFIG_FILE"
                ;;
            optimized)
                run_performance_test "optimized" "$CONFIG_FILE"
                ;;
            both)
                echo -e "${YELLOW}注意: 完整测试需要先运行基准版本，再运行优化版本${NC}"
                echo "请手动切换代码版本并分别运行测试"
                echo ""
                echo "建议步骤:"
                echo "1. git stash  # 保存当前优化代码"
                echo "2. git checkout HEAD~1  # 切换到优化前版本"
                echo "3. $0 -t baseline  # 运行基准测试"
                echo "4. git stash pop  # 恢复优化代码"
                echo "5. $0 -t optimized  # 运行优化测试"
                echo "6. $0 -r  # 比较结果"
                ;;
            *)
                echo -e "${RED}❌ 未知的测试类型: $test_type${NC}"
                show_help
                exit 1
                ;;
        esac
    fi
}

# 运行主程序
main "$@"
