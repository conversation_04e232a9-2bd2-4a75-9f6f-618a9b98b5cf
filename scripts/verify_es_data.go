package main

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/sirupsen/logrus"
)

// ESVerifier Elasticsearch数据验证工具
type ESVerifier struct {
	client *elasticsearch.Client
	logger *logrus.Logger
}

// NewESVerifier 创建ES验证工具
func NewESVerifier(address, username, password string) (*ESVerifier, error) {
	cfg := elasticsearch.Config{
		Addresses: []string{address},
		Username:  username,
		Password:  password,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	client, err := elasticsearch.NewClient(cfg)
	if err != nil {
		return nil, fmt.Errorf("创建ES客户端失败: %w", err)
	}

	return &ESVerifier{
		client: client,
		logger: logrus.New(),
	}, nil
}

// VerifyIndexData 验证索引数据
func (v *ESVerifier) VerifyIndexData(indexName string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 1. 检查索引是否存在
	existsRes, err := v.client.Indices.Exists([]string{indexName})
	if err != nil {
		return fmt.Errorf("检查索引存在性失败: %w", err)
	}
	defer existsRes.Body.Close()

	if existsRes.StatusCode != 200 {
		return fmt.Errorf("索引 %s 不存在", indexName)
	}

	v.logger.Infof("✅ 索引 %s 存在", indexName)

	// 2. 获取索引统计信息
	statsRes, err := v.client.Indices.Stats(
		v.client.Indices.Stats.WithIndex(indexName),
		v.client.Indices.Stats.WithContext(ctx),
	)
	if err != nil {
		return fmt.Errorf("获取索引统计失败: %w", err)
	}
	defer statsRes.Body.Close()

	var stats map[string]interface{}
	if err := json.NewDecoder(statsRes.Body).Decode(&stats); err != nil {
		return fmt.Errorf("解析索引统计失败: %w", err)
	}

	// 解析统计信息
	if indices, ok := stats["indices"].(map[string]interface{}); ok {
		if indexStats, ok := indices[indexName].(map[string]interface{}); ok {
			if total, ok := indexStats["total"].(map[string]interface{}); ok {
				if docs, ok := total["docs"].(map[string]interface{}); ok {
					if count, ok := docs["count"].(float64); ok {
						v.logger.Infof("📊 索引 %s 文档数量: %.0f", indexName, count)
					}
					if size, ok := docs["deleted"].(float64); ok {
						v.logger.Infof("🗑️  索引 %s 已删除文档数量: %.0f", indexName, size)
					}
				}
				if store, ok := total["store"].(map[string]interface{}); ok {
					if sizeInBytes, ok := store["size_in_bytes"].(float64); ok {
						v.logger.Infof("💾 索引 %s 存储大小: %.0f bytes", indexName, sizeInBytes)
					}
				}
			}
		}
	}

	// 3. 使用 _count API 获取精确文档数量
	countRes, err := v.client.Count(
		v.client.Count.WithIndex(indexName),
		v.client.Count.WithContext(ctx),
	)
	if err != nil {
		return fmt.Errorf("获取文档数量失败: %w", err)
	}
	defer countRes.Body.Close()

	var countResult map[string]interface{}
	if err := json.NewDecoder(countRes.Body).Decode(&countResult); err != nil {
		return fmt.Errorf("解析文档数量失败: %w", err)
	}

	if count, ok := countResult["count"].(float64); ok {
		v.logger.Infof("🔢 索引 %s 精确文档数量: %.0f", indexName, count)
		if count == 0 {
			v.logger.Warnf("⚠️  索引 %s 中没有文档，可能存在数据写入问题", indexName)
		}
	}

	// 4. 尝试搜索一些文档
	searchRes, err := v.client.Search(
		v.client.Search.WithIndex(indexName),
		v.client.Search.WithSize(5),
		v.client.Search.WithContext(ctx),
	)
	if err != nil {
		return fmt.Errorf("搜索文档失败: %w", err)
	}
	defer searchRes.Body.Close()

	var searchResult map[string]interface{}
	if err := json.NewDecoder(searchRes.Body).Decode(&searchResult); err != nil {
		return fmt.Errorf("解析搜索结果失败: %w", err)
	}

	if hits, ok := searchResult["hits"].(map[string]interface{}); ok {
		if total, ok := hits["total"].(map[string]interface{}); ok {
			if value, ok := total["value"].(float64); ok {
				v.logger.Infof("🔍 搜索结果总数: %.0f", value)
			}
		}
		if hitsList, ok := hits["hits"].([]interface{}); ok {
			v.logger.Infof("📄 返回文档数量: %d", len(hitsList))
			for i, hit := range hitsList {
				if hitMap, ok := hit.(map[string]interface{}); ok {
					if id, ok := hitMap["_id"].(string); ok {
						v.logger.Infof("  文档 %d ID: %s", i+1, id)
					}
				}
			}
		}
	}

	return nil
}

func main() {
	if len(os.Args) < 5 {
		fmt.Println("用法: go run verify_es_data.go <ES_ADDRESS> <USERNAME> <PASSWORD> <INDEX_NAME>")
		fmt.Println("示例: go run verify_es_data.go http://************:9200 elastic G08bMcIwjL starship-tke-scanner-2025-08-28")
		os.Exit(1)
	}

	address := os.Args[1]
	username := os.Args[2]
	password := os.Args[3]
	indexName := os.Args[4]

	verifier, err := NewESVerifier(address, username, password)
	if err != nil {
		fmt.Printf("创建验证工具失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("🔍 开始验证索引: %s\n", indexName)
	if err := verifier.VerifyIndexData(indexName); err != nil {
		fmt.Printf("❌ 验证失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ 验证完成")
}
