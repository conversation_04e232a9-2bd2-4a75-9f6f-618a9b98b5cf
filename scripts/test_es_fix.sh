#!/bin/bash

# Elasticsearch 修复验证脚本
# 用于测试数据写入和查询问题的修复效果

set -e

# 配置参数
ES_HOST="http://************:9200"
ES_USER="elastic"
ES_PASS="G08bMcIwjL"
DATE="2025-08-28"
INDEX_PREFIX="starship"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Elasticsearch 数据写入修复验证脚本${NC}"
echo "=================================="

# 函数：检查 ES 连接
check_es_connection() {
    echo -e "${BLUE}📡 检查 Elasticsearch 连接...${NC}"
    
    response=$(curl -s -u "$ES_USER:$ES_PASS" "$ES_HOST/_cluster/health" || echo "ERROR")
    
    if [[ "$response" == "ERROR" ]]; then
        echo -e "${RED}❌ 无法连接到 Elasticsearch${NC}"
        exit 1
    fi
    
    status=$(echo "$response" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}✅ Elasticsearch 连接成功，集群状态: $status${NC}"
}

# 函数：检查索引状态
check_index_status() {
    local index_name=$1
    echo -e "${BLUE}📊 检查索引状态: $index_name${NC}"
    
    # 检查索引是否存在
    if curl -s -u "$ES_USER:$ES_PASS" -o /dev/null -w "%{http_code}" "$ES_HOST/$index_name" | grep -q "200"; then
        echo -e "${GREEN}✅ 索引存在${NC}"
        
        # 获取文档数量
        count_response=$(curl -s -u "$ES_USER:$ES_PASS" "$ES_HOST/$index_name/_count")
        doc_count=$(echo "$count_response" | grep -o '"count":[0-9]*' | cut -d':' -f2)
        
        if [[ -n "$doc_count" ]]; then
            if [[ "$doc_count" -gt 0 ]]; then
                echo -e "${GREEN}✅ 文档数量: $doc_count${NC}"
            else
                echo -e "${RED}❌ 文档数量为 0${NC}"
            fi
        else
            echo -e "${YELLOW}⚠️  无法获取文档数量${NC}"
        fi
        
        # 获取索引统计信息
        stats_response=$(curl -s -u "$ES_USER:$ES_PASS" "$ES_HOST/$index_name/_stats")
        size_bytes=$(echo "$stats_response" | grep -o '"size_in_bytes":[0-9]*' | head -1 | cut -d':' -f2)
        
        if [[ -n "$size_bytes" ]]; then
            size_mb=$((size_bytes / 1024 / 1024))
            echo -e "${BLUE}📦 索引大小: ${size_bytes} bytes (${size_mb} MB)${NC}"
        fi
        
        # 尝试搜索文档
        search_response=$(curl -s -u "$ES_USER:$ES_PASS" "$ES_HOST/$index_name/_search?size=1")
        search_total=$(echo "$search_response" | grep -o '"total":{"value":[0-9]*' | cut -d':' -f3)
        
        if [[ -n "$search_total" ]]; then
            echo -e "${BLUE}🔍 搜索结果总数: $search_total${NC}"
        fi
        
    else
        echo -e "${RED}❌ 索引不存在${NC}"
    fi
    echo ""
}

# 函数：运行扫描程序
run_scanner() {
    echo -e "${BLUE}🚀 运行 CMDB Scanner...${NC}"
    
    if [[ -f "cmd/main.go" ]]; then
        echo "使用配置文件: config2.yaml"
        go run cmd/main.go -config config2.yaml
        echo -e "${GREEN}✅ Scanner 运行完成${NC}"
    else
        echo -e "${RED}❌ 找不到 cmd/main.go 文件${NC}"
        echo "请确保在项目根目录运行此脚本"
        exit 1
    fi
}

# 函数：验证修复效果
verify_fix() {
    echo -e "${BLUE}🔍 验证修复效果...${NC}"
    
    # 定义要检查的索引
    indices=(
        "${INDEX_PREFIX}-tke-scanner-${DATE}"
        "${INDEX_PREFIX}-unknown-scanner-${DATE}"
        "${INDEX_PREFIX}-eks-scanner-${DATE}"
    )
    
    local total_docs=0
    local indices_with_data=0
    
    for index in "${indices[@]}"; do
        echo -e "${YELLOW}检查索引: $index${NC}"
        
        if curl -s -u "$ES_USER:$ES_PASS" -o /dev/null -w "%{http_code}" "$ES_HOST/$index" | grep -q "200"; then
            count_response=$(curl -s -u "$ES_USER:$ES_PASS" "$ES_HOST/$index/_count")
            doc_count=$(echo "$count_response" | grep -o '"count":[0-9]*' | cut -d':' -f2)
            
            if [[ -n "$doc_count" && "$doc_count" -gt 0 ]]; then
                echo -e "${GREEN}  ✅ 有数据: $doc_count 个文档${NC}"
                total_docs=$((total_docs + doc_count))
                indices_with_data=$((indices_with_data + 1))
            else
                echo -e "${RED}  ❌ 无数据${NC}"
            fi
        else
            echo -e "${YELLOW}  📋 索引不存在${NC}"
        fi
    done
    
    echo ""
    echo -e "${BLUE}📈 验证结果汇总:${NC}"
    echo -e "  有数据的索引数量: ${indices_with_data}"
    echo -e "  总文档数量: ${total_docs}"
    
    if [[ $total_docs -gt 0 ]]; then
        echo -e "${GREEN}🎉 修复验证成功！数据可以正常查询${NC}"
        return 0
    else
        echo -e "${RED}❌ 修复验证失败！仍然无法查询到数据${NC}"
        return 1
    fi
}

# 函数：清理旧数据
cleanup_old_data() {
    echo -e "${BLUE}🧹 清理旧数据...${NC}"
    
    indices=(
        "${INDEX_PREFIX}-tke-scanner-${DATE}"
        "${INDEX_PREFIX}-unknown-scanner-${DATE}"
        "${INDEX_PREFIX}-eks-scanner-${DATE}"
    )
    
    for index in "${indices[@]}"; do
        if curl -s -u "$ES_USER:$ES_PASS" -o /dev/null -w "%{http_code}" "$ES_HOST/$index" | grep -q "200"; then
            echo "删除索引: $index"
            curl -s -u "$ES_USER:$ES_PASS" -X DELETE "$ES_HOST/$index" > /dev/null
            echo -e "${GREEN}✅ 已删除${NC}"
        fi
    done
}

# 主执行流程
main() {
    echo -e "${BLUE}开始验证流程...${NC}"
    echo ""
    
    # 1. 检查连接
    check_es_connection
    echo ""
    
    # 2. 检查当前状态
    echo -e "${BLUE}📋 检查当前索引状态${NC}"
    check_index_status "${INDEX_PREFIX}-tke-scanner-${DATE}"
    check_index_status "${INDEX_PREFIX}-unknown-scanner-${DATE}"
    
    # 3. 询问是否重新运行
    read -p "是否重新运行 Scanner 来测试修复效果？(y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 4. 清理旧数据
        cleanup_old_data
        echo ""
        
        # 5. 运行扫描程序
        run_scanner
        echo ""
        
        # 6. 等待一下让数据刷新
        echo -e "${BLUE}⏳ 等待 3 秒让数据刷新...${NC}"
        sleep 3
        echo ""
        
        # 7. 验证修复效果
        if verify_fix; then
            echo -e "${GREEN}🎉 恭喜！Elasticsearch 数据写入问题已修复${NC}"
        else
            echo -e "${RED}❌ 问题仍然存在，需要进一步排查${NC}"
            echo ""
            echo -e "${YELLOW}建议检查：${NC}"
            echo "1. 查看程序运行日志中的刷新和验证信息"
            echo "2. 检查 ES 集群健康状态"
            echo "3. 验证网络连接和认证信息"
        fi
    else
        echo -e "${BLUE}跳过重新运行，仅检查当前状态${NC}"
        verify_fix
    fi
}

# 检查依赖
if ! command -v curl &> /dev/null; then
    echo -e "${RED}❌ 需要安装 curl${NC}"
    exit 1
fi

if ! command -v go &> /dev/null; then
    echo -e "${RED}❌ 需要安装 Go${NC}"
    exit 1
fi

# 运行主程序
main
