# 多阶段构建
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache git ca-certificates tzdata

# 复制go mod文件
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build \
    -ldflags="-w -s -X main.version=$(git describe --tags --always --dirty 2>/dev/null || echo 'docker') -X main.buildTime=$(date -u '+%Y-%m-%d_%H:%M:%S') -X main.gitCommit=$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')" \
    -o cmdb-scanner ./cmd

# 最终镜像
FROM alpine:latest

# 安装运行时依赖
RUN apk --no-cache add ca-certificates tzdata

# 创建非root用户
RUN addgroup -g 1000 appgroup && \
    adduser -D -s /bin/sh -u 1000 -G appgroup appuser

# 设置工作目录
WORKDIR /app

# 复制二进制文件
COPY --from=builder /app/cmdb-scanner /app/cmdb-scanner

# 复制配置文件
COPY --from=builder /app/config.yaml /app/config.yaml

# 创建必要的目录
RUN mkdir -p /app/logs /app/output /app/work /app/temp && \
    chown -R appuser:appgroup /app

# 切换到非root用户
USER appuser

# 暴露端口（如果需要HTTP服务）
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD /app/cmdb-scanner --version || exit 1

# 设置环境变量
ENV TZ=UTC \
    LOG_LEVEL=INFO \
    CONFIG_PATH=/app/config.yaml

# 默认命令
ENTRYPOINT ["/app/cmdb-scanner"]
CMD ["-c", "/app/config.yaml"]