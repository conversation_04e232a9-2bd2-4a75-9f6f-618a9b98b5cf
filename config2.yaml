clickhouse:
  host: "*************"
  port: 9000
  database: "tkedata"
  username: "TKE_SRE"
  password: "TKE_CloudNativeSRE666"
  connect_timeout: "30s"
  max_connections: 10
  debug: false

# MySQL CMDB配置
mysql:
  host: "************"
  port: 3306
  database: "cmdb"
  username: "cmdb"
  password: "zmBLP#VvHFV7+iA11x3_"
  connect_timeout: "60s"
  max_connections: 20
  debug: true

# Elasticsearch配置
elasticsearch:
  addresses:
    - "http://************:9200/"
  username: "elastic"
  password: "G08bMcIwjL"
  index_prefix: "starship"
  timeout: "60s"
  max_retries: 5
  debug: true
  batch_size: 2000
  sync_timeout: "600s"
  verify_ssl: false
  ca_cert_path: ""

# 用户集群组件配置
user_cluster_components:
  # kubernetes-proxy相关组件
  kubernetes-proxy:
    workload_name: "kubernetes-proxy"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "kubernetes-proxy"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

# Meta集群组件配置
meta_cluster_components:
  # TKE ENI IP Webhook
  kube-scheduler:
    workload_name: "scheduler"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "Meta集群调度器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  kube-apiserver:
    workload_name: "kube-apiserver"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "Meta集群apiserver"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: [ ]

  kube-controller-manager:
    workload_name: "controller-manager"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "Meta集群控制器管理器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: [ ]

  eklet:
    workload_name: "eklet"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "EKS eklet组件"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: [ ]

  eks-controller:
    workload_name: "eks-controllers"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "EKS eklet controller组件"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: [ ]

  cloud-controller-manager:
    workload_name: "cloud-controller-manager"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "cloud-controller-manager组件"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: [ ]

  ipamd-webhook-controller:
    workload_name: "ipamd-webhook-controller"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "ipamd-webhook-controller组件"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: [ ]

  service-controller:
    workload_name: "service-controller"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "service-controller组件"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: [ ]

  cluster-monitor:
    workload_name: "cluster-monitor"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "集群监控"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: [ ]

  cluster-autoscaler:
    workload_name: "cluster-autoscaler"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "集群自动扩缩容"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: [ ]

# 检查器配置
inspectors:
  multi_az_distribution:
    enabled: true
    name: "多AZ分布检查"
    description: "检查workload是否跨多个可用区部署以提高可用性"
    timeout: "300s"

# 扫描配置
scan:
  # 扫描日期配置
  date_range:
    # 扫描模式: latest(最新), yesterday(昨天), range(范围), specific(指定日期)
    mode: "latest"
    # 指定日期模式下的日期 (YYYY-MM-DD)
    specific_date: ""
    # 范围模式下的开始和结束日期
    start_date: ""
    end_date: ""

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/cmdb_scanner.log"
  max_file_size: "100MB"
  backup_count: 5
  console_output: true

# 输出配置
output:
  # 可选格式: console, json, yaml, elasticsearch
  formats: ["elasticsearch"]
  
  # 控制台输出配置
  console:
    show_summary: true
  
  # JSON输出配置
  json:
    file_path: "output/scan_results.json"
    pretty_print: true
    classification:
      primary: "none"    # 可选: cluster_type, region, none
      secondary: "status"        # 固定: status (通过/未通过)
      tertiary: "region"         # 可选: region, none
  
  # YAML输出配置
  yaml:
    file_path: "output/scan_results.yaml"
  
  # Elasticsearch输出配置
  elasticsearch:
    enabled: true
    index_template: "{index_prefix}-cmdb-scan-{date}"
    doc_type: "_doc"
    include_metadata: true

# 系统配置
system:
  # 工作目录
  work_dir: "./work"
  
  # 临时文件目录
  temp_dir: "./temp"