FROM golang:1.23

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone
RUN dpkg-reconfigure -f noninteractive tzdata

# 复制二进制文件
ADD ./bin/cmdb-scanner /usr/bin/cmdb-scanner

# 创建必要的目录
RUN mkdir -p /app/logs /app/output /app/work /app/temp

# 设置工作目录
WORKDIR /app

# 复制配置文件
COPY config.yaml /app/config.yaml

# 暴露端口（如果有API服务的话）
EXPOSE 8080

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD /usr/bin/cmdb-scanner --version || exit 1

# 启动命令
CMD ["cmdb-scanner", "-c", "/app/config.yaml"]