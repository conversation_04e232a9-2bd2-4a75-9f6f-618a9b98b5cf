clickhouse:
  host: "*************"
  port: 9000
  database: "tkedata"
  username: "TKE_SRE"
  password: "TKE_CloudNativeSRE666"
  connect_timeout: "30s"
  max_connections: 10
  debug: false

# MySQL CMDB配置
mysql:
  host: "************"
  port: 3306
  database: "cmdb"
  username: "cmdb"
  password: "zmBLP#VvHFV7+iA11x3_"
  connect_timeout: "60s"
  max_connections: 20
  debug: true

# Elasticsearch配置
elasticsearch:
  addresses: 
    - "https://lb-8c0obspy-9t8hen40zgwxklpv.clb.gz-tencentclb.cloud:9200"
  username: "elastic"
  password: "Q37F3WO6rEreT4Tr"
  index_prefix: "starship"
  timeout: "30s"
  max_retries: 3
  debug: true
  batch_size: 1000
  sync_timeout: "300s"
  verify_ssl: false
  ca_cert_path: ""

# 用户集群组件配置
user_cluster_components:
  # CBS存储相关组件
  cbs:
    workload_name: "csi-cbs-controller"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "CBS存储控制器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  cbs-node:
    workload_name: "csi-cbs-node"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "CBS存储节点组件"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # CFS存储相关组件
  cfs:
    workload_name: "csi-attacher-cfsplugin"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "CFS存储附加器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  cfs-nodeplugin:
    workload_name: "csi-nodeplugin-cfsplugin"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "CFS节点插件"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  cfs-provisioner:
    workload_name: "csi-provisioner-cfsplugin"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "CFS存储供应器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # CFS Turbo存储组件
  cfsturbo:
    workload_name: "cfsturbo-csi-controller"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "CFS Turbo控制器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  cfsturbo-node:
    workload_name: "cfsturbo-csi-node"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "CFS Turbo节点组件"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # COS存储相关组件
  cos:
    workload_name: "csi-cosplugin-external-runner"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "COS外部运行器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  cos-launcher:
    workload_name: "csi-coslauncher"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "COS启动器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  cos-plugin:
    workload_name: "csi-cosplugin"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "COS插件"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # 边缘计算相关组件
  edge-headless:
    workload_name: "statefulset-grid-daemon"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "边缘无头服务守护进程"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  edge-headless-job:
    workload_name: "grid-daemon-job"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "边缘守护进程任务"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  edge-health:
    workload_name: "edge-health"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "边缘健康检查"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  edge-health-taint:
    workload_name: "taint-admission"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "边缘污点准入控制"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # 外部边缘组件
  externaledge-coredns:
    workload_name: "coredns"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "外部边缘CoreDNS"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  externaledge-flannel:
    workload_name: "flannel"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "外部边缘Flannel网络"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  externaledge-affinity:
    workload_name: "modify-edge-affinity"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "边缘亲和性修改器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  externaledge-proxy:
    workload_name: "edge-kube-proxy"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "边缘Kube代理"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  externaledge-tunnel:
    workload_name: "tunnel-edge"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "边缘隧道"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # 网络相关组件
  flannel:
    workload_name: "tunnel-proxy"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "Flannel隧道代理"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # 节点问题检测
  npdplus:
    workload_name: "node-problem-detector"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "节点问题检测器增强版"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # OOM防护
  oomguard:
    workload_name: "oom-guard"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "OOM防护组件"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # QoS代理
  qosagent:
    workload_name: "qos-agent"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "服务质量代理"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # ENI IP管理相关组件
  eniipamd:
    workload_name: "tke-eni-agent"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "ENI代理"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  eniipamd-scheduler:
    workload_name: "tke-eni-ip-scheduler"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "ENI IP调度器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  eniipamd-ipamd:
    workload_name: "tke-eni-ipamd"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "ENI IP地址管理守护进程"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  eniipamd-cleaner:
    workload_name: "tke-eni-ipamd-cleaner"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "ENI IP清理器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # Crane调度器相关组件
  cranescheduler:
    workload_name: "crane-scheduler-controller"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "Crane调度控制器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  cranescheduler-descheduler:
    workload_name: "crane-descheduler"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "Crane反调度器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  cranescheduler-scheduler:
    workload_name: "crane-scheduler"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "Crane调度器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # TKE X Crane调度器
  tkex-cranescheduler:
    workload_name: "crane-scheduler-controller"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "TKE X Crane调度控制器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  tkex-cranescheduler-scheduler:
    workload_name: "crane-scheduler"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "TKE X Crane调度器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # 外部密钥管理
  externalsecrets:
    workload_name: "externalsecrets-cert-controller"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "外部密钥证书控制器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  externalsecrets-main:
    workload_name: "externalsecrets"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "外部密钥管理"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  externalsecrets-webhook:
    workload_name: "externalsecrets-webhook"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "外部密钥Webhook"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # DNS相关组件
  dnsautoscaler:
    workload_name: "tke-dns-autoscaler"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "DNS自动扩缩容"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  localdns:
    workload_name: "node-local-dns"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "节点本地DNS"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  coredns:
    workload_name: "coredns"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "CoreDNS服务"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # 网络组件
  kubeproxy:
    workload_name: "kube-proxy"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "Kubernetes代理"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  networkpolicy:
    workload_name: "networkpolicy"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "网络策略"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # Ingress控制器
  ingressnginx:
    workload_name: "tke-ingress-nginx-controller-operator"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "TKE Ingress Nginx控制器操作器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # 监控相关组件
  monitor:
    workload_name: "agenttke-monitor-agent"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "TKE监控代理"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # GPU相关组件
  qgpu:
    workload_name: "elastic-gpu-exporter"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "弹性GPU导出器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  qgpu-manager:
    workload_name: "qgpu-manager"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "QGPU管理器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  qgpu-scheduler:
    workload_name: "qgpu-scheduler"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "QGPU调度器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # EKS相关组件
  eklete:
    workload_name: "klet"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "EKS Klet组件"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # 安全相关组件
  cerberus:
    workload_name: "cerberus-webhook"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "Cerberus安全Webhook"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # Crane守护进程
  craned:
    workload_name: "craned"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "Crane守护进程"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # HPC相关组件
  hpc:
    workload_name: "tke-hpc-controller"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "TKE HPC控制器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # IMC操作器
  imc-operator:
    workload_name: "imc-operator-controller-manager"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "IMC操作器控制管理器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # P2P相关组件
  p2p:
    workload_name: "p2p-proxy"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "P2P代理"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  p2p-agent:
    workload_name: "p2p-agent"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "P2P代理"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # 安全组策略
  securitygrouppolicy:
    workload_name: "securitygrouppolocy"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "安全组策略"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # 备份相关组件
  tke-backup:
    workload_name: "vilero"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "TKE备份Vilero"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  tke-backup-agent:
    workload_name: "node-agent"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "TKE备份节点代理"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # HPC控制器相关
  tke-hpc-controller:
    workload_name: "tke-hpc-controller"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "TKE HPC控制器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  tke-hpc-crd:
    workload_name: "delete-hpc-crd"
    component_type: "user"
    deployment_location: "用户集群"
    data_source: "clickhouse"
    description: "TKE HPC CRD清理器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

# Meta集群组件配置
meta_cluster_components:
  # TKE ENI IP Webhook
  tke-eni-ip-webhook:
    workload_name: "add-pod-eni-ip-limit-webhook"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "TKE ENI IP Webhook"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # 集群监控
  cluster-monitor:
    workload_name: "cluster-monitor"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "集群监控"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # 集群自动扩缩容
  cluster-autoscaler:
    workload_name: "cluster-autoscaler"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "集群自动扩缩容"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # EKS控制器
  eks-controllers:
    workload_name: "klet"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "EKS Klet组件"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  # Meta核心组件
  meta_component-etcd:
    workload_name: "etcd"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "ETCD数据库"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  meta_component-apiserver:
    workload_name: "kube-apiserver"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "Meta集群API服务器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  meta_component-controller:
    workload_name: "kube-controller-manager"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "Meta集群控制器管理器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  meta_component-scheduler:
    workload_name: "kube-scheduler"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "Meta集群调度器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

  meta_component-cloud:
    workload_name: "cloud-controller-manager"
    component_type: "meta"
    deployment_location: "Meta集群"
    data_source: "mysql"
    description: "Meta集群云控制器管理器"
    enabled: true
    priority: 1
    fuzzy_search: false
    disabled_inspectors: []

# 检查器配置
inspectors:
  multi_az_distribution:
    enabled: true
    name: "多AZ分布检查"
    description: "检查workload是否跨多个可用区部署以提高可用性"
    timeout: "300s"

# 扫描配置
scan:
  # 扫描日期配置
  date_range:
    # 扫描模式: latest(最新), yesterday(昨天), range(范围), specific(指定日期)
    mode: "latest"
    # 指定日期模式下的日期 (YYYY-MM-DD)
    specific_date: ""
    # 范围模式下的开始和结束日期
    start_date: ""
    end_date: ""

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/cmdb_scanner.log"
  max_file_size: "100MB"
  backup_count: 5
  console_output: true

# 输出配置
output:
  # 可选格式: console, json, yaml, elasticsearch
  formats: ["console", "json", "yaml", "elasticsearch"]
  
  # 控制台输出配置
  console:
    show_summary: true
  
  # JSON输出配置
  json:
    file_path: "output/scan_results.json"
    pretty_print: true
    classification:
      primary: "none"    # 可选: cluster_type, region, none
      secondary: "status"        # 固定: status (通过/未通过)
      tertiary: "region"         # 可选: region, none
  
  # YAML输出配置
  yaml:
    file_path: "output/scan_results.yaml"
  
  # Elasticsearch输出配置
  elasticsearch:
    enabled: true
    index_template: "{index_prefix}-cmdb-scan-{date}"
    doc_type: "_doc"
    include_metadata: true

# 系统配置
system:
  # 工作目录
  work_dir: "./work"
  
  # 临时文件目录
  temp_dir: "./temp"