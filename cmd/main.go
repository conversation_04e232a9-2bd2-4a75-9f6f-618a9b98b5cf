package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"cmdb-scanner/internal/batch"
	"cmdb-scanner/internal/config"
	"cmdb-scanner/internal/database"
	"cmdb-scanner/internal/output"
	"cmdb-scanner/internal/types"

	"github.com/sirupsen/logrus"
	"gopkg.in/yaml.v3"
)

func setupLogging(cfg *config.Config) *logrus.Logger {
	logger := logrus.New()
	level, err := logrus.ParseLevel(cfg.Logging.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	logFile := cfg.Logging.FilePath
	if err := os.MkdirAll(filepath.Dir(logFile), 0755); err != nil {
		fmt.Printf("警告: 无法创建日志目录: %v\n", err)
	}

	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		fmt.Printf("警告: 无法打开日志文件: %v\n", err)
	} else {
		logger.SetOutput(io.MultiWriter(os.Stdout, file))
	}

	return logger
}

func getScanDate(cfg *config.Config) time.Time {
	dateConfig := cfg.Scan.DateRange
	mode := dateConfig["mode"]
	if mode == "" {
		mode = "yesterday"
	}

	now := time.Now()
	switch mode {
	case "latest":
		return now
	case "yesterday":
		return now.AddDate(0, 0, -1)
	case "specific":
		if specificDate, ok := dateConfig["specific_date"].(string); ok && specificDate != "" {
			if date, err := time.Parse("2006-01-02", specificDate); err == nil {
				return date
			}
		}
		fmt.Println("警告: 未指定具体日期，使用昨天")
		return now.AddDate(0, 0, -1)
	case "range":
		if startDate, ok := dateConfig["start_date"].(string); ok && startDate != "" {
			if date, err := time.Parse("2006-01-02", startDate); err == nil {
				return date
			}
		}
		fmt.Println("警告: 未指定开始日期，使用昨天")
		return now.AddDate(0, 0, -1)
	default:
		fmt.Printf("警告: 未知的日期模式: %s，使用昨天\n", mode)
		return now.AddDate(0, 0, -1)
	}
}

// getAllClusterIDs 获取所有相关的cluster_id
func getAllClusterIDs(chClient *database.ClickHouseClient, scanDate time.Time, logger *logrus.Logger) ([]string, error) {
	logger.Info("开始获取所有cluster_id")

	// 直接从ClickHouse cluster表获取所有cluster_id，使用DISTINCT去重
	clusterIDs, err := chClient.GetAllClusterIDs(scanDate)
	if err != nil {
		return nil, fmt.Errorf("从ClickHouse获取cluster_id失败: %w", err)
	}

	logger.Infof("从ClickHouse获取到 %d 个cluster_id", len(clusterIDs))
	return clusterIDs, nil
}

// splitIntoBatches 将cluster_id列表分批
func splitIntoBatches(clusterIDs []string, batchSize int) [][]string {
	var batches [][]string
	for i := 0; i < len(clusterIDs); i += batchSize {
		end := i + batchSize
		if end > len(clusterIDs) {
			end = len(clusterIDs)
		}
		batches = append(batches, clusterIDs[i:end])
	}
	return batches
}

func runInspectors(cfg *config.Config, chClient *database.ClickHouseClient, mysqlClient *database.MySQLClient, scanDate time.Time, logger *logrus.Logger) []map[string]interface{} {
	logger.Info("🚀 开始新的批量处理架构")

	// 1. 获取所有相关的cluster_id
	allClusterIDs, err := getAllClusterIDs(chClient, scanDate, logger)
	if err != nil {
		logger.Errorf("❌ 获取cluster_id失败: %v", err)
		return []map[string]interface{}{}
	}

	if len(allClusterIDs) == 0 {
		logger.Warning("⚠️  未找到任何cluster_id")
		return []map[string]interface{}{}
	}

	logger.Infof("📊 获取到 %d 个cluster_id", len(allClusterIDs))

	// 2. 按50个cluster_id分批（减少批量大小以避免MySQL查询超时）
	batches := splitIntoBatches(allClusterIDs, 50)
	logger.Infof("📦 分为 %d 个批次，每批最多50个集群", len(batches))

	// 3. 创建结果收集channel
	resultChan := make(chan *types.BatchResult, len(batches))

	// 4. 为每批创建协程
	for i, batchClusterIDs := range batches {
		go func(batchID int, clusterIDs []string) {
			logger.Debugf("🔄 启动批次 %d 处理，集群数量: %d", batchID, len(clusterIDs))

			processor := batch.NewClusterBatchProcessor(
				clusterIDs,
				batchID,
				chClient,
				mysqlClient,
				cfg,
				scanDate,
			)

			result, err := processor.Process()
			if err != nil {
				logger.Errorf("❌ 批次 %d 处理失败: %v", batchID, err)
				result = &types.BatchResult{
					BatchID:   batchID,
					Error:     err,
					Results:   make(map[string]*types.ClusterResult),
					Timestamp: time.Now(),
				}
			}

			resultChan <- result
		}(i, batchClusterIDs)
	}

	// 5. 收集所有结果并转换为兼容格式
	var allResults []map[string]interface{}
	successfulBatches := 0
	failedBatches := 0

	for i := 0; i < len(batches); i++ {
		batchResult := <-resultChan
		if batchResult.Error == nil {
			// 将BatchResult转换为兼容的格式
			convertedResults := convertBatchResultToLegacyFormat(batchResult, cfg, logger)
			allResults = append(allResults, convertedResults...)
			successfulBatches++
			logger.Debugf("✅ 批次 %d 处理成功，生成 %d 个结果", batchResult.BatchID, len(convertedResults))
		} else {
			failedBatches++
			logger.Errorf("❌ 批次 %d 处理失败: %v", batchResult.BatchID, batchResult.Error)
		}
	}

	logger.Infof("🎉 批量处理完成: 成功 %d 批次, 失败 %d 批次, 总结果 %d 个",
		successfulBatches, failedBatches, len(allResults))

	return allResults
}

// convertBatchResultToLegacyFormat 将BatchResult转换为兼容的旧格式
func convertBatchResultToLegacyFormat(batchResult *types.BatchResult, cfg *config.Config, logger *logrus.Logger) []map[string]interface{} {
	var results []map[string]interface{}

	for _, clusterResult := range batchResult.Results {
		// 为每个组件的每个检查器结果创建一个记录
		for componentName, componentResult := range clusterResult.ComponentResults {
			for _, inspectionResult := range componentResult.InspectionResults {
				// 构建兼容的结果格式
				legacyResult := map[string]interface{}{
					"inspector_id":   inspectionResult.InspectorID,
					"inspector_name": inspectionResult.InspectorName,
					"status":         inspectionResult.Status,
					"message":        inspectionResult.Message,
					"workload_info": map[string]interface{}{
						"component_name": componentName,
						"workload_name":  getWorkloadNameFromConfig(componentName, componentResult.ComponentType, cfg),
						"component_type": componentResult.ComponentType,
						"priority":       getPriorityFromConfig(componentName, componentResult.ComponentType, cfg),
					},
					"details": buildDetailsFromInspectionResult(clusterResult, inspectionResult),
					"cluster_metadata": map[string]interface{}{
						"cluster_id":   clusterResult.ClusterMetadata.ClusterID,
						"cluster_name": clusterResult.ClusterMetadata.ClusterName,
						"cluster_type": clusterResult.ClusterMetadata.ClusterType,
						"region":       clusterResult.ClusterMetadata.Region,
						"app_id":       clusterResult.ClusterMetadata.AppID,
						"version":      clusterResult.ClusterMetadata.Version,
						"status":       clusterResult.ClusterMetadata.Status,
					},
				}

				results = append(results, legacyResult)
			}
		}
	}

	logger.Debugf("🔄 转换批次结果: %d 个集群 -> %d 个兼容记录", len(batchResult.Results), len(results))
	return results
}

// getWorkloadNameFromConfig 从配置中获取workload名称
func getWorkloadNameFromConfig(componentName, componentType string, cfg *config.Config) string {
	switch componentType {
	case "user_cluster":
		if component, exists := cfg.UserClusterComponents[componentName]; exists {
			return component.WorkloadName
		}
	case "meta_cluster":
		if component, exists := cfg.MetaClusterComponents[componentName]; exists {
			return component.WorkloadName
		}
	}

	// 向后兼容：检查旧的components配置
	if component, exists := cfg.Components[componentName]; exists {
		return component.WorkloadName
	}

	return "unknown-workload"
}

// getPriorityFromConfig 从配置中获取priority
func getPriorityFromConfig(componentName, componentType string, cfg *config.Config) int {
	switch componentType {
	case "user_cluster":
		if component, exists := cfg.UserClusterComponents[componentName]; exists {
			return component.Priority
		}
	case "meta_cluster":
		if component, exists := cfg.MetaClusterComponents[componentName]; exists {
			return component.Priority
		}
	}

	// 向后兼容：检查旧的components配置
	if component, exists := cfg.Components[componentName]; exists {
		return component.Priority
	}

	return 1 // 默认优先级
}

// buildDetailsFromInspectionResult 构建详细信息
func buildDetailsFromInspectionResult(clusterResult *types.ClusterResult, inspectionResult types.InspectionResult) map[string]interface{} {
	details := make(map[string]interface{})

	// 只添加检查结果的详细信息，不包含cluster级别信息
	for key, value := range inspectionResult.Details {
		// 跳过cluster级别的信息，这些应该在cluster_metadata中
		if key == "cluster_id" || key == "cluster_name" || key == "cluster_type" ||
			key == "region" || key == "app_id" || key == "version" || key == "status" {
			continue
		}
		details[key] = value
	}

	return details
}

// determineComponentTypeByWorkload 根据workload_name确定组件类型（从配置文件中获取）
func determineComponentTypeByWorkload(workloadName string, cfg *config.Config) string {
	// 检查用户集群组件
	for _, component := range cfg.UserClusterComponents {
		if component.WorkloadName == workloadName {
			return "user_cluster"
		}
	}

	// 检查Meta集群组件
	for _, component := range cfg.MetaClusterComponents {
		if component.WorkloadName == workloadName {
			return "meta_cluster"
		}
	}

	return "user_cluster" // 默认为用户集群组件
}

// outputConsole 控制台输出
func outputConsole(results []map[string]interface{}, cfg *config.Config, _ *logrus.Logger) {
	consoleConfig := cfg.Output.Console
	if !consoleConfig["show_summary"].(bool) {
		return
	}

	if len(results) == 0 {
		fmt.Println("\n" + strings.Repeat("=", 60))
		fmt.Println("CMDB Scanner 扫描结果")
		fmt.Println(strings.Repeat("=", 60))
		fmt.Println("未获取到任何扫描结果")
		fmt.Println(strings.Repeat("=", 60))
		return
	}

	// 分别统计用户集群组件和Meta集群组件
	userComponentStats := make(map[string]map[string]map[string]map[string]int)
	metaComponentStats := make(map[string]map[string]map[string]map[string]int)
	unknownClusterStats := make(map[string]map[string]int)           // 单独统计未知集群
	unknownReasonStats := make(map[string]map[string]map[string]int) // 统计unknown原因：componentDisplay -> inspectorName -> reason -> count

	for _, result := range results {
		details, _ := result["details"].(map[string]interface{})
		workloadInfo, _ := result["workload_info"].(map[string]interface{})
		clusterMetadata, _ := result["cluster_metadata"].(map[string]interface{})

		componentName, _ := workloadInfo["component_name"].(string)
		if componentName == "" {
			componentName = "Unknown"
		}
		workloadName, _ := workloadInfo["workload_name"].(string)
		if workloadName == "" {
			workloadName = "Unknown"
		}
		inspectorName, _ := result["inspector_name"].(string)
		if inspectorName == "" {
			inspectorName = "Unknown"
		}
		clusterType, _ := clusterMetadata["cluster_type"].(string)
		originalClusterType := clusterType // 保存原始值用于调试

		// 标准化集群类型
		if clusterType == "unknown" {
			clusterType = "unknown"
		} else if clusterType == "tke" {
			clusterType = "tke"
		} else if clusterType == "eks" {
			clusterType = "eks"
		} else {
			// 对于空值或其他非标准值，归类为unknown
			if clusterType == "" || clusterType == "null" {
				fmt.Printf("DEBUG: 集群 cluster_type 为空或null: '%s'，归类为unknown\n", originalClusterType)
				clusterType = "unknown"
			} else {
				fmt.Printf("DEBUG: 集群 cluster_type 值异常: '%s'，归类为unknown\n", originalClusterType)
				clusterType = "unknown"
			}
		}
		status, _ := result["status"].(string)
		if status == "" {
			status = "unknown"
		}

		componentDisplay := fmt.Sprintf("%s (%s)", componentName, workloadName)

		// 根据workload_name确定组件类型
		componentType := determineComponentTypeByWorkload(workloadName, cfg)

		// 调试信息：打印前几个组件的分类情况
		if len(userComponentStats)+len(metaComponentStats) < 5 {
			fmt.Printf("DEBUG: 组件='%s' workload='%s' 类型='%s'\n", componentName, workloadName, componentType)
		}

		// 收集unknown原因统计（不影响正常的集群类型统计）
		if unknownReason, exists := details["unknown_reason"]; exists && unknownReason != "" {
			if _, exists := unknownReasonStats[componentDisplay]; !exists {
				unknownReasonStats[componentDisplay] = make(map[string]map[string]int)
			}
			if _, exists := unknownReasonStats[componentDisplay][inspectorName]; !exists {
				unknownReasonStats[componentDisplay][inspectorName] = make(map[string]int)
			}
			reasonStr := fmt.Sprintf("%v", unknownReason)
			unknownReasonStats[componentDisplay][inspectorName][reasonStr]++
		}

		// 收集not_deployed原因统计
		if notDeployedReason, exists := details["not_deployed_reason"]; exists && notDeployedReason != "" {
			if _, exists := unknownReasonStats[componentDisplay]; !exists {
				unknownReasonStats[componentDisplay] = make(map[string]map[string]int)
			}
			if _, exists := unknownReasonStats[componentDisplay][inspectorName]; !exists {
				unknownReasonStats[componentDisplay][inspectorName] = make(map[string]int)
			}
			reasonStr := "没有部署该组件"
			unknownReasonStats[componentDisplay][inspectorName][reasonStr]++
		}

		// 根据组件类型选择统计对象
		var componentStats map[string]map[string]map[string]map[string]int
		if componentType == "meta_cluster" {
			componentStats = metaComponentStats
		} else {
			componentStats = userComponentStats
		}

		if _, exists := componentStats[componentDisplay]; !exists {
			componentStats[componentDisplay] = make(map[string]map[string]map[string]int)
		}
		if _, exists := componentStats[componentDisplay][inspectorName]; !exists {
			componentStats[componentDisplay][inspectorName] = make(map[string]map[string]int)
		}
		if _, exists := componentStats[componentDisplay][inspectorName][clusterType]; !exists {
			componentStats[componentDisplay][inspectorName][clusterType] = map[string]int{
				"total":            0,
				"passed":           0,
				"multinode_total":  0,
				"multinode_passed": 0,
			}
		}

		stats := componentStats[componentDisplay][inspectorName][clusterType]
		stats["total"]++
		if status == "passed" {
			stats["passed"]++
		}

		multinode, _ := details["multinode"].(bool)
		stats["multinode_total"]++
		if multinode {
			stats["multinode_passed"]++
		}
	}

	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("CMDB Scanner 扫描结果")
	fmt.Println(strings.Repeat("=", 60))

	// 调试信息：显示统计对象的大小
	fmt.Printf("DEBUG: userComponentStats 大小: %d, metaComponentStats 大小: %d\n", len(userComponentStats), len(metaComponentStats))

	// 先显示用户集群组件
	if len(userComponentStats) > 0 {
		fmt.Println("【用户集群组件】")
		printComponentStats(userComponentStats, unknownClusterStats, unknownReasonStats)
	}

	// 再显示Meta集群组件
	if len(metaComponentStats) > 0 {
		fmt.Println("【Meta集群组件】")
		printComponentStats(metaComponentStats, unknownClusterStats, unknownReasonStats)
	}

	fmt.Println(strings.Repeat("=", 60))
}

// getKeysFromUnknownReasonStats 获取unknownReasonStats的键列表（用于调试）
func getKeysFromUnknownReasonStats(unknownReasonStats map[string]map[string]map[string]int) []string {
	keys := make([]string, 0, len(unknownReasonStats))
	for k := range unknownReasonStats {
		keys = append(keys, k)
	}
	return keys
}

// printComponentStats 打印组件统计信息
func printComponentStats(componentStats map[string]map[string]map[string]map[string]int, unknownClusterStats map[string]map[string]int, unknownReasonStats map[string]map[string]map[string]int) {
	for componentDisplay, inspectors := range componentStats {
		fmt.Printf("\n%s:\n", componentDisplay)

		for inspectorName, clusterTypes := range inspectors {
			fmt.Printf("  %s:\n", inspectorName)

			// 按指定顺序显示集群类型：tke, eks, 然后其他
			orderedTypes := []string{"tke", "eks"}

			// 先显示TKE和EKS集群的通过率 - 严格按顺序
			for _, clusterType := range orderedTypes {
				if stats, exists := clusterTypes[clusterType]; exists {
					total := stats["total"]
					passed := stats["passed"]
					passRate := 0.0
					if total > 0 {
						passRate = float64(passed) / float64(total) * 100
					}

					multinodeTotal := stats["multinode_total"]
					multinodePassed := stats["multinode_passed"]
					multinodeRate := 0.0
					if multinodeTotal > 0 {
						multinodeRate = float64(multinodePassed) / float64(multinodeTotal) * 100
					}

					clusterTypeText := fmt.Sprintf("%s集群通过率", strings.ToLower(clusterType))
					passRateText := fmt.Sprintf("%.0f%% (%d/%d)", passRate, passed, total)
					multinodeText := fmt.Sprintf("不同节点分布率: %.0f%% (%d/%d)", multinodeRate, multinodePassed, multinodeTotal)

					fmt.Printf("    %-25s %-25s %s\n", clusterTypeText+":", passRateText, multinodeText)
				}
			}

			// 最后显示unknown集群（确保在tke、eks之后）
			if stats, exists := clusterTypes["unknown"]; exists {

				total := stats["total"]
				passed := stats["passed"]
				passRate := 0.0
				if total > 0 {
					passRate = float64(passed) / float64(total) * 100
				}

				multinodeTotal := stats["multinode_total"]
				multinodePassed := stats["multinode_passed"]
				multinodeRate := 0.0
				if multinodeTotal > 0 {
					multinodeRate = float64(multinodePassed) / float64(multinodeTotal) * 100
				}

				fmt.Printf("    unknown集群通过率: %15.0f%% (%d/%d) %12s 不同节点分布率: %0.0f%% (%d/%d)\n",
					passRate, passed, total, "", multinodeRate, multinodePassed, multinodeTotal)
			}

			// 显示unknown原因的详细统计
			if reasonStats, exists := unknownReasonStats[componentDisplay][inspectorName]; exists && len(reasonStats) > 0 {
				fmt.Printf("    检查失败clusterid原因:\n")
				for reason, count := range reasonStats {
					// 对于"没有运行中的Pod"类型的原因，只显示统计，不显示具体集群
					if strings.Contains(reason, "没有运行中的") && strings.Contains(reason, "Pod") {
						fmt.Printf("        - 没有部署该组件: %d个集群\n", count)
					} else {
						fmt.Printf("        - %s: %d个集群\n", reason, count)
					}
				}
			}

			// 显示未知集群统计（在所有集群类型之后）
			if count, exists := unknownClusterStats[componentDisplay][inspectorName]; exists && count > 0 {
				fmt.Printf("    未知集群: %d个\n", count)
			}
		}
	}

	fmt.Println(strings.Repeat("=", 60))
}

// outputJSON JSON输出
func outputJSON(results []map[string]interface{}, cfg *config.Config, logger *logrus.Logger) {
	jsonConfig := cfg.Output.JSON
	filePath := jsonConfig["file_path"].(string)
	if filePath == "" {
		filePath = "output/scan_results.json"
	}

	if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
		logger.Errorf("创建输出目录失败: %v", err)
		return
	}

	try := func() {
		// 使用新的格式化函数
		formatter := &output.OutputFormatter{}
		clusterOutput := formatter.FormatClusterOutput(results)

		// 直接使用分类结构，时间戳在ExportToMultipleFormats中处理

		classification := jsonConfig["classification"].(map[string]interface{})
		primary := classification["primary"].(string)
		if primary == "" {
			primary = "none"
		}

		var outputData interface{}
		if primary == "none" {
			outputData = clusterOutput
		} else {
			secondary := classification["secondary"].(string)
			if secondary == "" {
				secondary = "status"
			}
			tertiary := classification["tertiary"].(string)
			if tertiary == "" {
				tertiary = "none"
			}
			outputData = classifyResults(results, primary, secondary, tertiary)
		}

		file, err := os.Create(filePath)
		if err != nil {
			logger.Errorf("创建JSON文件失败: %v", err)
			return
		}
		defer file.Close()

		encoder := json.NewEncoder(file)
		if prettyPrint, ok := jsonConfig["pretty_print"].(bool); ok && prettyPrint {
			encoder.SetIndent("", "  ")
		}
		encoder.SetEscapeHTML(false)

		if err := encoder.Encode(outputData); err != nil {
			logger.Errorf("写入JSON文件失败: %v", err)
			return
		}

		logger.Infof("结果已保存到JSON文件: %s", filePath)
	}

	try()
}

// classifyResults 分类结果
func classifyResults(results []map[string]interface{}, primary, secondary, tertiary string) map[string]interface{} {
	classified := make(map[string]interface{})

	for _, result := range results {
		details, _ := result["details"].(map[string]interface{})

		primaryKey := getClassificationKey(result, primary)
		secondaryKey := getClassificationKey(result, secondary)
		tertiaryKey := ""
		if tertiary != "none" {
			tertiaryKey = getClassificationKey(result, tertiary)
		}

		if _, exists := classified[primaryKey]; !exists {
			classified[primaryKey] = make(map[string]interface{})
		}

		primaryMap := classified[primaryKey].(map[string]interface{})
		if _, exists := primaryMap[secondaryKey]; !exists {
			if tertiary != "none" {
				primaryMap[secondaryKey] = make(map[string]interface{})
			} else {
				primaryMap[secondaryKey] = map[string]interface{}{
					"clusters": []map[string]interface{}{},
				}
			}
		}

		clusterInfo := map[string]interface{}{
			"cluster_id":   details["cluster_id"],
			"cluster_name": details["cluster_name"],
			"message":      result["message"],
			"details":      details,
		}

		if tertiary != "none" {
			secondaryMap := primaryMap[secondaryKey].(map[string]interface{})
			if _, exists := secondaryMap[tertiaryKey]; !exists {
				secondaryMap[tertiaryKey] = []map[string]interface{}{}
			}
			tertiaryList := secondaryMap[tertiaryKey].([]map[string]interface{})
			secondaryMap[tertiaryKey] = append(tertiaryList, clusterInfo)
		} else {
			secondaryMap := primaryMap[secondaryKey].(map[string]interface{})
			clusters := secondaryMap["clusters"].([]map[string]interface{})
			secondaryMap["clusters"] = append(clusters, clusterInfo)
		}
	}

	return classified
}

// getClassificationKey 获取分类键
func getClassificationKey(result map[string]interface{}, classificationType string) string {
	details, _ := result["details"].(map[string]interface{})

	switch classificationType {
	case "cluster_type":
		if clusterType, ok := details["cluster_type"].(string); ok {
			return clusterType
		}
		return "Unknown"
	case "region":
		if region, ok := details["region"].(string); ok {
			return region
		}
		return "Unknown"
	case "status":
		if status, ok := result["status"].(string); ok {
			if status == "passed" {
				return "passed"
			}
			return "failed"
		}
		return "unknown"
	default:
		return "all"
	}
}

// outputYAML YAML输出
func outputYAML(results []map[string]interface{}, cfg *config.Config, logger *logrus.Logger) {
	yamlConfig := cfg.Output.YAML
	filePath := yamlConfig["file_path"].(string)
	if filePath == "" {
		filePath = "output/scan_results.yaml"
	}

	if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
		logger.Errorf("创建输出目录失败: %v", err)
		return
	}

	try := func() {
		// 使用新的格式化函数
		formatter := &output.OutputFormatter{}
		clusterOutput := formatter.FormatClusterOutput(results)

		// 直接使用分类结构，时间戳在ExportToMultipleFormats中处理

		file, err := os.Create(filePath)
		if err != nil {
			logger.Errorf("创建YAML文件失败: %v", err)
			return
		}
		defer file.Close()

		encoder := yaml.NewEncoder(file)
		encoder.SetIndent(2)
		if err := encoder.Encode(clusterOutput); err != nil {
			logger.Errorf("写入YAML文件失败: %v", err)
			return
		}

		logger.Infof("结果已保存到YAML文件: %s", filePath)
	}

	try()
}

// outputElasticsearch Elasticsearch输出
func outputElasticsearch(results []map[string]interface{}, cfg *config.Config, scanDate time.Time, logger *logrus.Logger) []string {
	esConfig := cfg.Output.Elasticsearch
	if !esConfig["enabled"].(bool) {
		logger.Info("Elasticsearch输出未启用")
		return []string{}
	}

	esClient := database.GetElasticsearchClient(&cfg.Elasticsearch)
	logger.Warning("esClient:", cfg.Elasticsearch.Addresses[0], " ", cfg.Elasticsearch.Password)
	if esClient == nil || !esClient.IsConnected() {
		logger.Warning("Elasticsearch未连接, 跳过输出")
		return []string{}
	}

	indexedNames := esClient.IndexScanResults(results, scanDate)
	if len(indexedNames) > 0 {
		logger.Infof("成功将结果索引到Elasticsearch，创建的索引: %s", strings.Join(indexedNames, ", "))
	} else {
		logger.Error("Elasticsearch索引失败")
	}

	return indexedNames
}

// main 主函数
func main() {
	var configPath string
	flag.StringVar(&configPath, "c", "config.yaml", "配置文件路径")
	flag.Parse()

	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		fmt.Printf("错误: 配置文件 '%s' 不存在\n", configPath)
		fmt.Println("请确保配置文件存在，或使用 -c 参数指定正确的配置文件路径")
		fmt.Println("示例: ./cmdb-scanner -c /path/to/your/config.yaml")
		os.Exit(1)
	}

	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		fmt.Printf("加载配置文件失败: %v\n", err)
		os.Exit(1)
	}

	logger := setupLogging(cfg)
	logger.Info("CMDB Scanner 启动")
	logger.Infof("配置文件: %s", configPath)

	// 初始化数据库连接
	chClient, err := database.NewClickHouseClient(&cfg.ClickHouse)
	if err != nil {
		logger.Errorf("无法连接到ClickHouse数据库: %v", err)
		os.Exit(1)
	}

	// 初始化MySQL客户端
	mysqlClient, err := database.NewMySQLClient(&cfg.MySQL)
	if err != nil {
		logger.Errorf("无法连接到MySQL数据库: %v", err)
		os.Exit(1)
	}

	scanDate := getScanDate(cfg)
	logger.Infof("扫描日期: %s", scanDate.Format("2006-01-02"))

	// 创建工作目录
	if err := os.MkdirAll(cfg.System.WorkDir, 0755); err != nil {
		logger.Errorf("创建工作目录失败: %v", err)
		os.Exit(1)
	}

	// 创建临时目录
	if err := os.MkdirAll(cfg.System.TempDir, 0755); err != nil {
		logger.Errorf("创建临时目录失败: %v", err)
		os.Exit(1)
	}

	logger.Info("开始运行检查器")
	results := runInspectors(cfg, chClient, mysqlClient, scanDate, logger)
	logger.Infof("检查器运行完成，共获得 %d 个结果", len(results))

	outputFormats := cfg.Output.Formats
	var elasticsearchIndices []string

	for _, format := range outputFormats {
		switch format {
		case "console":
			outputConsole(results, cfg, logger)
		case "json":
			outputJSON(results, cfg, logger)
		case "yaml":
			outputYAML(results, cfg, logger)
		case "elasticsearch":
			elasticsearchIndices = outputElasticsearch(results, cfg, scanDate, logger)
		}
	}

	// 在汇总日志中包含Elasticsearch索引信息
	if len(elasticsearchIndices) > 0 {
		logger.Infof("CMDB Scanner 完成，Elasticsearch索引: %s", strings.Join(elasticsearchIndices, ", "))
	} else {
		logger.Info("CMDB Scanner 完成")
	}
}
