package inspectors

import (
	"cmdb-scanner/internal/types"
	"context"
	"time"
)

// BatchInspector 批量检查器接口
type BatchInspector interface {
	// InspectComponentBatch 批量检查：传入多个clusterID和组件名，返回每个cluster的检查结果
	InspectComponentBatch(ctx context.Context, clusterIDs []string, componentName string) (map[string]types.InspectionResult, error)

	// SetDataSource 设置数据源
	SetDataSource(dataSource interface{})

	// GetInspectorInfo 获取检查器信息
	GetInspectorInfo() types.InspectorInfo

	// SetScanDate 设置扫描日期
	SetScanDate(scanDate time.Time)
}

// ComponentInspectionTask 组件检查任务
type ComponentInspectionTask struct {
	ClusterID     string
	ComponentName string
	ComponentType string
	WorkloadName  string
	InspectorID   string
	DataSource    string
}

// ComponentInspectionResult 组件检查结果
type ComponentInspectionResult struct {
	ClusterID     string
	ComponentName string
	ComponentType string
	InspectorID   string
	Result        types.InspectionResult
	Error         error
}
