package inspectors

import (
	"context"
	"fmt"
	"time"

	"cmdb-scanner/internal/cache"
	"cmdb-scanner/internal/config"
	"cmdb-scanner/internal/database"
	"cmdb-scanner/internal/types"

	"github.com/sirupsen/logrus"
)

// MetaClusterBatchMultiAZInspector Meta集群批量多AZ检查器（空实现框架）
type MetaClusterBatchMultiAZInspector struct {
	mysqlClient   *database.MySQLClient
	scanDate      time.Time
	inspectorInfo types.InspectorInfo
	appConfig     *config.Config
	logger        *logrus.Logger
	cacheManager  *cache.DataCacheManager
}

// NewMetaClusterBatchMultiAZInspector 创建Meta集群批量多AZ检查器
func NewMetaClusterBatchMultiAZInspector(mysqlClient *database.MySQLClient, config *config.Config, scanDate time.Time, logger *logrus.Logger, cacheManager *cache.DataCacheManager) *MetaClusterBatchMultiAZInspector {
	return &MetaClusterBatchMultiAZInspector{
		mysqlClient:  mysqlClient,
		appConfig:    config,
		scanDate:     scanDate,
		logger:       logger,
		cacheManager: cacheManager,
		inspectorInfo: types.InspectorInfo{
			ID:          "multi_az_distribution",
			Name:        "多AZ分布检查",
			Description: "检查Meta集群workload是否跨多个可用区部署以提高可用性",
			Timeout:     300 * time.Second,
		},
	}
}

// InspectComponentBatch 批量检查组件
func (i *MetaClusterBatchMultiAZInspector) InspectComponentBatch(ctx context.Context, clusterIDs []string, componentName string) (map[string]types.InspectionResult, error) {
	if len(clusterIDs) == 0 {
		return map[string]types.InspectionResult{}, nil
	}

	i.logger.Infof("[MetaClusterBatchMultiAZInspector] 🔍 开始批量检查Meta组件: %d个集群, 组件: %s", len(clusterIDs), componentName)
	i.logger.Infof("[MetaClusterBatchMultiAZInspector] 📋 前5个集群ID示例: %v", clusterIDs[:min(5, len(clusterIDs))])

	// 1. 获取workload名称
	workloadName, err := i.getWorkloadNameByComponent(componentName)
	if err != nil {
		i.logger.Errorf("[MetaClusterBatchMultiAZInspector] ❌ 获取workload名称失败: %v", err)
		return nil, fmt.Errorf("获取workload名称失败: %w", err)
	}
	i.logger.Infof("[MetaClusterBatchMultiAZInspector] 📋 组件 %s 对应的workload: %s", componentName, workloadName)

	// 2. 从缓存获取Meta组件数据
	i.logger.Infof("[MetaClusterBatchMultiAZInspector] 🔍 从缓存获取Meta组件数据: workload=%s, 日期=%s", workloadName, i.scanDate.Format("2006-01-02"))
	metaData, err := i.cacheManager.GetMetaComponentData(workloadName)
	if err != nil {
		i.logger.Errorf("[MetaClusterBatchMultiAZInspector] ❌ 从缓存获取Meta组件数据失败: %v", err)
		return nil, fmt.Errorf("从缓存获取Meta组件数据失败: %w", err)
	}
	i.logger.Infof("[MetaClusterBatchMultiAZInspector] 📊 Meta组件数据获取完成: 获得%d个集群的数据", len(metaData.ClusterComponentData))

	// 3. 逐个cluster分析
	results := make(map[string]types.InspectionResult)
	statusCount := make(map[string]int)

	for _, clusterID := range clusterIDs {
		result := i.analyzeMetaClusterAZDistribution(clusterID, componentName, metaData)
		results[clusterID] = result
		statusCount[result.Status]++
	}

	i.logger.Infof("[MetaClusterBatchMultiAZInspector] 📊 批量检查完成: %d个结果", len(results))
	i.logger.Infof("[MetaClusterBatchMultiAZInspector] 📈 状态统计: %v", statusCount)
	return results, nil
}

// SetDataSource 设置数据源
func (i *MetaClusterBatchMultiAZInspector) SetDataSource(dataSource interface{}) {
	if mysqlClient, ok := dataSource.(*database.MySQLClient); ok {
		i.mysqlClient = mysqlClient
	}
}

// GetInspectorInfo 获取检查器信息
func (i *MetaClusterBatchMultiAZInspector) GetInspectorInfo() types.InspectorInfo {
	return i.inspectorInfo
}

// SetScanDate 设置扫描日期
func (i *MetaClusterBatchMultiAZInspector) SetScanDate(scanDate time.Time) {
	i.scanDate = scanDate
}

// SetAppConfig 设置应用配置
func (i *MetaClusterBatchMultiAZInspector) SetAppConfig(cfg *config.Config) {
	i.appConfig = cfg
}

// createNoPodsResult 创建无Pod的检查结果
func (i *MetaClusterBatchMultiAZInspector) createNoPodsResult(clusterID, componentName string) types.InspectionResult {
	i.logger.Infof("[createNoPodsResult] Meta集群 %s 的组件 %s 没有运行的Pod", clusterID, componentName)
	return types.InspectionResult{
		InspectorID:   i.inspectorInfo.ID,
		InspectorName: i.inspectorInfo.Name,
		Status:        "not_deployed", // 改为not_deployed状态
		Message:       fmt.Sprintf("Meta集群 %s 没有部署 %s 组件", clusterID, componentName),
		Details: map[string]interface{}{
			"component_name":      componentName,
			"running_pod_count":   0,
			"unique_zone_count":   0,
			"multinode":           false,
			"zones":               []string{}, // 无Pod时返回空zone列表
			"multi_az_status":     "not_deployed",
			"not_deployed_reason": fmt.Sprintf("Meta集群 %s 没有部署 %s 组件", clusterID, componentName),
		},
	}
}

// 以下是为后续实现预留的方法框架

// getWorkloadNameByComponent 根据组件名获取workload名称（预留）
func (i *MetaClusterBatchMultiAZInspector) getWorkloadNameByComponent(componentName string) (string, error) {
	if i.appConfig == nil {
		return "", fmt.Errorf("应用配置未设置")
	}

	// 从Meta集群组件配置中查找
	if comp, exists := i.appConfig.MetaClusterComponents[componentName]; exists {
		return comp.WorkloadName, nil
	}

	return "", fmt.Errorf("未找到Meta集群组件 %s 的配置", componentName)
}

// 注意：batchQueryMetaComponentData 方法已移除，现在使用缓存管理器获取数据

// analyzeMetaClusterAZDistribution 分析Meta集群AZ分布
func (i *MetaClusterBatchMultiAZInspector) analyzeMetaClusterAZDistribution(
	clusterID string,
	componentName string,
	metaData *types.MetaComponentDataBatch,
) types.InspectionResult {
	i.logger.Infof("[analyzeMetaClusterAZDistribution] 🔍 分析Meta集群 %s 的组件 %s", clusterID, componentName)

	// 获取该集群的组件信息
	componentInfo, exists := metaData.ClusterComponentData[clusterID]
	if !exists {
		i.logger.Warnf("[analyzeMetaClusterAZDistribution] ⚠️  集群 %s 未找到组件数据", clusterID)
		return i.createNoPodsResult(clusterID, componentName)
	}

	// 调试：检查cluster_type的值
	if componentInfo.ClusterType == "" || componentInfo.ClusterType == "null" {
		i.logger.Warnf("[analyzeMetaClusterAZDistribution] ⚠️  集群 %s 的ClusterType为空或null: '%s'", clusterID, componentInfo.ClusterType)
		// 如果cluster_type异常，也应该设置unknown_reason
		return types.InspectionResult{
			InspectorID:   i.inspectorInfo.ID,
			InspectorName: i.inspectorInfo.Name,
			Status:        "unknown",
			Message:       fmt.Sprintf("Meta集群 %s 的 %s cluster_type异常: '%s'", clusterID, componentName, componentInfo.ClusterType),
			Details: map[string]interface{}{
				"cluster_id":        clusterID,
				"cluster_type":      "unknown",
				"component_name":    componentName,
				"running_pod_count": componentInfo.PodCount,
				"unique_zone_count": componentInfo.ZoneCount,
				"multinode":         false,
				"zones":             componentInfo.Zones,
				"multi_az_status":   "cluster_type_error",
				"unknown_reason":    fmt.Sprintf("cluster_type异常: '%s'", componentInfo.ClusterType),
			},
		}
	} else if componentInfo.ClusterType != "tke" && componentInfo.ClusterType != "eks" {
		i.logger.Warnf("[analyzeMetaClusterAZDistribution] ⚠️  集群 %s 的ClusterType值异常: '%s' (不是tke或eks)", clusterID, componentInfo.ClusterType)
		// 如果cluster_type不是预期值，也应该设置unknown_reason
		return types.InspectionResult{
			InspectorID:   i.inspectorInfo.ID,
			InspectorName: i.inspectorInfo.Name,
			Status:        "unknown",
			Message:       fmt.Sprintf("Meta集群 %s 的 %s cluster_type值异常: '%s'", clusterID, componentName, componentInfo.ClusterType),
			Details: map[string]interface{}{
				"component_name":    componentName,
				"running_pod_count": componentInfo.PodCount,
				"unique_zone_count": componentInfo.ZoneCount,
				"multinode":         false,
				"zones":             componentInfo.Zones,
				"multi_az_status":   "cluster_type_error",
				"unknown_reason":    fmt.Sprintf("cluster_type值异常: '%s' (不是tke或eks)", componentInfo.ClusterType),
			},
		}
	} else {
		// i.logger.Infof("[analyzeMetaClusterAZDistribution] ✅ 集群 %s 的ClusterType正常: '%s'", clusterID, componentInfo.ClusterType)
	}

	// 如果没有运行的Pod
	if componentInfo.PodCount == 0 {
		// i.logger.Warnf("[analyzeMetaClusterAZDistribution] ⚠️  集群 %s 的组件 %s 没有运行的Pod", clusterID, componentName)
		return i.createNoPodsResult(clusterID, componentName)
	}

	i.logger.Infof("[analyzeMetaClusterAZDistribution] 📊 集群 %s: 运行Pod数=%d, AZ数=%d", clusterID, componentInfo.PodCount, componentInfo.ZoneCount)

	// 基本逻辑：如果zone_count > 1，则认为实现了多AZ分布
	var multiAZStatus string
	var status string
	var message string

	if componentInfo.PodCount == 1 {
		multiAZStatus = "false"
		status = "failed"
		message = fmt.Sprintf("Meta集群 %s 的 %s 未实现多AZ分布：只有1个Pod", clusterID, componentName)
		i.logger.Infof("[analyzeMetaClusterAZDistribution] ❌ 失败原因: 只有1个Pod，无法实现多AZ分布")
	} else if componentInfo.ZoneCount >= 2 {
		multiAZStatus = "passed"
		status = "passed"
		message = fmt.Sprintf("Meta集群 %s 的 %s 实现了多AZ分布：%d个zone", clusterID, componentName, componentInfo.ZoneCount)
		i.logger.Infof("[analyzeMetaClusterAZDistribution] ✅ 通过: 分布在%d个AZ", componentInfo.ZoneCount)
	} else {
		multiAZStatus = "false"
		status = "failed"
		message = fmt.Sprintf("Meta集群 %s 的 %s 未实现多AZ分布：所有Pod都在同一个AZ", clusterID, componentName)
		i.logger.Infof("[analyzeMetaClusterAZDistribution] ❌ 失败: 所有Pod都在同一个AZ，zoneCount=%d", componentInfo.ZoneCount)
	}

	return types.InspectionResult{
		InspectorID:   i.inspectorInfo.ID,
		InspectorName: i.inspectorInfo.Name,
		Status:        status,
		Message:       message,
		Details: map[string]interface{}{
			"component_name":    componentName,
			"running_pod_count": componentInfo.PodCount,
			"unique_zone_count": componentInfo.ZoneCount,
			"multinode":         componentInfo.ZoneCount > 1, // Meta集群用zone数量判断多节点
			"zones":             componentInfo.Zones,         // 返回具体的zone列表
			"multi_az_status":   multiAZStatus,
		},
	}
}
