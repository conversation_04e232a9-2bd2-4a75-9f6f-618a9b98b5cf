package inspectors

import (
	"context"
	"fmt"
	"strings"
	"time"

	"cmdb-scanner/internal/cache"
	"cmdb-scanner/internal/config"
	"cmdb-scanner/internal/database"
	"cmdb-scanner/internal/types"

	"github.com/sirupsen/logrus"
)

// UserClusterBatchMultiAZInspectorOptimized 优化的用户集群批量多AZ分布检查器
type UserClusterBatchMultiAZInspectorOptimized struct {
	clickhouseClient *database.ClickHouseClient
	appConfig        *config.Config
	scanDate         time.Time
	logger           *logrus.Logger
	cacheManager     *cache.DataCacheManager
	inspectorInfo    types.InspectorInfo
}

// NewUserClusterBatchMultiAZInspectorOptimized 创建优化的用户集群批量多AZ检查器
func NewUserClusterBatchMultiAZInspectorOptimized(
	clickhouseClient *database.ClickHouseClient,
	config *config.Config,
	scanDate time.Time,
	logger *logrus.Logger,
	cacheManager *cache.DataCacheManager,
) *UserClusterBatchMultiAZInspectorOptimized {
	return &UserClusterBatchMultiAZInspectorOptimized{
		clickhouseClient: clickhouseClient,
		appConfig:        config,
		scanDate:         scanDate,
		logger:           logger,
		cacheManager:     cacheManager,
		inspectorInfo: types.InspectorInfo{
			ID:          "multi_az_distribution",
			Name:        "多AZ分布检查",
			Description: "检查用户集群workload是否跨多个可用区部署以提高可用性",
			Timeout:     300 * time.Second,
		},
	}
}

// InspectComponentBatch 批量检查组件
func (i *UserClusterBatchMultiAZInspectorOptimized) InspectComponentBatch(ctx context.Context, clusterIDs []string, componentName string) (map[string]types.InspectionResult, error) {
	if len(clusterIDs) == 0 {
		return map[string]types.InspectionResult{}, nil
	}

	i.logger.Infof("[UserClusterBatchMultiAZInspector] 🔍 开始批量检查用户组件: %d个集群, 组件: %s", len(clusterIDs), componentName)
	i.logger.Infof("[UserClusterBatchMultiAZInspector] 📋 前5个集群ID示例: %v", clusterIDs[:min(5, len(clusterIDs))])

	// 1. 获取workload名称
	workloadName, err := i.getWorkloadNameByComponent(componentName)
	if err != nil {
		i.logger.Errorf("[UserClusterBatchMultiAZInspector] ❌ 获取workload名称失败: %v", err)
		return nil, fmt.Errorf("获取workload名称失败: %w", err)
	}
	i.logger.Infof("[UserClusterBatchMultiAZInspector] 📋 组件 %s 对应的workload: %s", componentName, workloadName)

	// 2. 从缓存获取Pod数据
	i.logger.Infof("[UserClusterBatchMultiAZInspector] 🔍 从缓存获取Pod数据: workload=%s, 日期=%s", workloadName, i.scanDate.Format("2006-01-02"))
	podData, err := i.cacheManager.GetPodData(workloadName)
	if err != nil {
		i.logger.Errorf("[UserClusterBatchMultiAZInspector] ❌ 从缓存获取Pod数据失败: %v", err)
		return nil, fmt.Errorf("从缓存获取Pod数据失败: %w", err)
	}
	i.logger.Infof("[UserClusterBatchMultiAZInspector] 📊 Pod数据获取完成: 获得%d个集群的数据", len(podData.ClusterPodData))

	// 3. 批量获取节点标签信息
	allNodeNames := podData.GetAllNodeNames()
	if len(allNodeNames) == 0 {
		i.logger.Warnf("[UserClusterBatchMultiAZInspector] ⚠️  没有找到任何节点")
		// 返回所有集群的no_pods状态
		results := make(map[string]types.InspectionResult)
		for _, clusterID := range clusterIDs {
			results[clusterID] = types.InspectionResult{
				InspectorID:   i.inspectorInfo.ID,
				InspectorName: i.inspectorInfo.Name,
				Status:        "no_pods",
				Message:       "未找到运行中的Pod",
				Details:       map[string]interface{}{},
			}
		}
		return results, nil
	}

	i.logger.Infof("[UserClusterBatchMultiAZInspector] 🔍 开始查询节点标签: %d个节点", len(allNodeNames))
	nodeLabels, err := i.clickhouseClient.BatchQueryNodeLabels(allNodeNames, i.scanDate)
	if err != nil {
		i.logger.Errorf("[UserClusterBatchMultiAZInspector] ❌ 批量查询节点标签失败: %v", err)
		return nil, fmt.Errorf("批量查询节点标签失败: %w", err)
	}
	i.logger.Infof("[UserClusterBatchMultiAZInspector] 📊 节点标签查询完成: 获得%d个节点的标签", len(nodeLabels))

	// 4. 逐个cluster分析
	results := make(map[string]types.InspectionResult)
	statusCount := make(map[string]int)

	for _, clusterID := range clusterIDs {
		result := i.analyzeClusterAZDistribution(clusterID, componentName, podData, nodeLabels)
		results[clusterID] = result
		statusCount[result.Status]++
	}

	i.logger.Infof("[UserClusterBatchMultiAZInspector] 📊 批量检查完成: %d个结果", len(results))
	i.logger.Infof("[UserClusterBatchMultiAZInspector] 📈 状态统计: %v", statusCount)
	return results, nil
}

// getWorkloadNameByComponent 根据组件名获取workload名称
func (i *UserClusterBatchMultiAZInspectorOptimized) getWorkloadNameByComponent(componentName string) (string, error) {
	if i.appConfig == nil {
		return "", fmt.Errorf("应用配置未设置")
	}

	component, exists := i.appConfig.UserClusterComponents[componentName]
	if !exists {
		return "", fmt.Errorf("未找到组件配置: %s", componentName)
	}

	if component.WorkloadName == "" {
		return "", fmt.Errorf("组件 %s 的workload名称为空", componentName)
	}

	return component.WorkloadName, nil
}

// analyzeClusterAZDistribution 分析集群AZ分布
func (i *UserClusterBatchMultiAZInspectorOptimized) analyzeClusterAZDistribution(
	clusterID string,
	componentName string,
	podData *types.PodDataBatch,
	nodeLabels map[string]types.NodeLabels,
) types.InspectionResult {
	
	// 获取该集群的Pod信息
	clusterPodInfo, exists := podData.ClusterPodData[clusterID]
	if !exists || len(clusterPodInfo.NodeNames) == 0 {
		return types.InspectionResult{
			InspectorID:   i.inspectorInfo.ID,
			InspectorName: i.inspectorInfo.Name,
			Status:        "no_pods",
			Message:       "集群中未找到运行中的Pod",
			Details: map[string]interface{}{
				"cluster_id":     clusterID,
				"component_name": componentName,
				"pod_count":      0,
				"node_count":     0,
				"zones":          []string{},
			},
		}
	}

	// 统计该集群中Pod所在的可用区
	zoneSet := make(map[string]bool)
	validNodeCount := 0
	
	for _, nodeName := range clusterPodInfo.NodeNames {
		if nodeLabel, exists := nodeLabels[nodeName]; exists && nodeLabel.Zone != "" {
			zoneSet[nodeLabel.Zone] = true
			validNodeCount++
		}
	}

	zones := make([]string, 0, len(zoneSet))
	for zone := range zoneSet {
		zones = append(zones, zone)
	}

	// 判断结果
	var status string
	var message string
	
	if len(zones) == 0 {
		status = "unknown"
		message = "无法获取节点的可用区信息"
	} else if len(zones) >= 2 {
		status = "passed"
		message = fmt.Sprintf("组件已部署在%d个可用区: %s", len(zones), strings.Join(zones, ", "))
	} else {
		status = "failed"
		message = fmt.Sprintf("组件仅部署在1个可用区: %s", zones[0])
	}

	return types.InspectionResult{
		InspectorID:   i.inspectorInfo.ID,
		InspectorName: i.inspectorInfo.Name,
		Status:        status,
		Message:       message,
		Details: map[string]interface{}{
			"cluster_id":       clusterID,
			"component_name":   componentName,
			"pod_count":        clusterPodInfo.TotalRunningPods,
			"node_count":       len(clusterPodInfo.NodeNames),
			"valid_node_count": validNodeCount,
			"zones":            zones,
			"zone_count":       len(zones),
		},
	}
}

// 注意：min 函数已在其他文件中定义，这里移除重复定义
