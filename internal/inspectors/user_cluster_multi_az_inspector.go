package inspectors

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"cmdb-scanner/internal/config"
	"cmdb-scanner/internal/database"
	"cmdb-scanner/internal/region"
	"cmdb-scanner/internal/types"

	"github.com/sirupsen/logrus"
)

// UserClusterBatchMultiAZInspector 用户集群批量多AZ检查器
type UserClusterBatchMultiAZInspector struct {
	clickhouseClient *database.ClickHouseClient
	scanDate         time.Time
	inspectorInfo    types.InspectorInfo
	appConfig        *config.Config
	logger           *logrus.Logger
}

// NewUserClusterBatchMultiAZInspector 创建用户集群批量多AZ检查器
func NewUserClusterBatchMultiAZInspector() *UserClusterBatchMultiAZInspector {
	return &UserClusterBatchMultiAZInspector{
		inspectorInfo: types.InspectorInfo{
			ID:          "multi_az_distribution",
			Name:        "多AZ分布检查",
			Description: "检查workload是否跨多个可用区部署以提高可用性",
			Timeout:     300 * time.Second,
		},
		logger: logrus.New(),
	}
}

// InspectComponentBatch 批量检查组件
func (i *UserClusterBatchMultiAZInspector) InspectComponentBatch(ctx context.Context, clusterIDs []string, componentName string) (map[string]types.InspectionResult, error) {
	if len(clusterIDs) == 0 {
		return map[string]types.InspectionResult{}, nil
	}

	i.logger.Infof("[InspectComponentBatch] 🔍 开始批量检查: %d个集群, 组件: %s", len(clusterIDs), componentName)
	// i.logger.Infof("[InspectComponentBatch] 📋 前5个集群ID示例: %v", clusterIDs[:min(5, len(clusterIDs))])

	// 1. 获取workload名称
	workloadName, err := i.getWorkloadNameByComponent(componentName)
	if err != nil {
		i.logger.Errorf("[InspectComponentBatch] ❌ 获取workload名称失败: %v", err)
		return nil, fmt.Errorf("获取workload名称失败: %w", err)
	}
	// i.logger.Infof("[InspectComponentBatch] 📋 组件 %s 对应的workload: %s", componentName, workloadName)

	// 2. 批量查询Pod数据
	// i.logger.Infof("[InspectComponentBatch] 🔍 开始查询Pod数据: workload=%s, 日期=%s", workloadName, i.scanDate.Format("2006-01-02"))
	podData, err := i.clickhouseClient.BatchQueryPodData(clusterIDs, workloadName, i.scanDate)
	if err != nil {
		// i.logger.Errorf("[InspectComponentBatch] ❌ 批量查询Pod数据失败: %v", err)
		return nil, fmt.Errorf("批量查询Pod数据失败: %w", err)
	}
	// i.logger.Infof("[InspectComponentBatch] 📊 Pod数据查询完成: 获得%d个集群的数据", len(podData.ClusterPodData))

	// 3. 批量查询节点标签
	allNodeNames := podData.GetAllNodeNames()
	// i.logger.Infof("[InspectComponentBatch] 🏷️  需要查询 %d 个节点的标签", len(allNodeNames))
	if len(allNodeNames) > 0 {
		i.logger.Infof("[InspectComponentBatch] 📋 前5个节点示例: %v", allNodeNames[:min(5, len(allNodeNames))])
	}
	nodeLabels, err := i.clickhouseClient.BatchQueryNodeLabels(allNodeNames, i.scanDate)
	if err != nil {
		// i.logger.Errorf("[InspectComponentBatch] ❌ 批量查询节点标签失败: %v", err)
		return nil, fmt.Errorf("批量查询节点标签失败: %w", err)
	}
	// i.logger.Infof("[InspectComponentBatch] 📊 节点标签查询完成: 获得%d个节点的标签", len(nodeLabels))

	// 4. 批量查询集群基本信息（作为region信息的备用数据源）
	// i.logger.Infof("[InspectComponentBatch] 🏢 开始查询集群基本信息")
	clusterInfoMap, err := i.clickhouseClient.GetClusterMetadataBatch(clusterIDs, i.scanDate)
	if err != nil {
		// i.logger.Errorf("[InspectComponentBatch] ❌ 批量查询集群信息失败: %v", err)
		return nil, fmt.Errorf("批量查询集群信息失败: %w", err)
	}
	// i.logger.Infof("[InspectComponentBatch] 📊 集群信息查询完成: 获得%d个集群的信息", len(clusterInfoMap))

	// 5. 逐个cluster分析
	results := make(map[string]types.InspectionResult)
	statusCount := make(map[string]int)

	for _, clusterID := range clusterIDs {
		result := i.analyzeClusterAZDistribution(clusterID, componentName, podData, nodeLabels, clusterInfoMap)
		results[clusterID] = result
		statusCount[result.Status]++
	}

	i.logger.Infof("[InspectComponentBatch] 📊 批量检查完成: %d个结果", len(results))
	i.logger.Infof("[InspectComponentBatch] 📈 状态统计: %v", statusCount)
	return results, nil
}

// SetDataSource 设置数据源
func (i *UserClusterBatchMultiAZInspector) SetDataSource(dataSource interface{}) {
	if chClient, ok := dataSource.(*database.ClickHouseClient); ok {
		i.clickhouseClient = chClient
	}
}

// GetInspectorInfo 获取检查器信息
func (i *UserClusterBatchMultiAZInspector) GetInspectorInfo() types.InspectorInfo {
	return i.inspectorInfo
}

// SetScanDate 设置扫描日期
func (i *UserClusterBatchMultiAZInspector) SetScanDate(scanDate time.Time) {
	i.scanDate = scanDate
}

// SetAppConfig 设置应用配置
func (i *UserClusterBatchMultiAZInspector) SetAppConfig(cfg *config.Config) {
	i.appConfig = cfg
}

// getWorkloadNameByComponent 根据组件名获取workload名称
func (i *UserClusterBatchMultiAZInspector) getWorkloadNameByComponent(componentName string) (string, error) {
	if i.appConfig == nil {
		return "", fmt.Errorf("应用配置未设置")
	}

	// 从用户集群组件配置中查找
	if comp, exists := i.appConfig.UserClusterComponents[componentName]; exists {
		return comp.WorkloadName, nil
	}

	// 向后兼容：从旧的components配置中查找
	if comp, exists := i.appConfig.Components[componentName]; exists {
		return comp.WorkloadName, nil
	}

	return "", fmt.Errorf("未找到组件 %s 的配置", componentName)
}

// analyzeClusterAZDistribution 分析单个集群的AZ分布
func (i *UserClusterBatchMultiAZInspector) analyzeClusterAZDistribution(
	clusterID string,
	componentName string,
	podData *types.PodDataBatch,
	nodeLabels map[string]types.NodeLabels,
	clusterInfoMap map[string]types.ClusterMetadata,
) types.InspectionResult {
	// i.logger.Infof("[analyzeClusterAZDistribution] 🔍 分析集群 %s 的组件 %s", clusterID, componentName)

	// 获取该集群的Pod信息
	podInfo, exists := podData.ClusterPodData[clusterID]
	if !exists {
		// i.logger.Warnf("[analyzeClusterAZDistribution] ⚠️  集群 %s 未找到Pod数据", clusterID)
		return i.createNoPodsResult(clusterID, componentName)
	}

	// 如果没有运行的Pod
	if podInfo.TotalRunningPods == 0 {
		// i.logger.Warnf("[analyzeClusterAZDistribution] ⚠️  集群 %s 的组件 %s 没有运行的Pod", clusterID, componentName)
		return i.createNoPodsResult(clusterID, componentName)
	}

	// i.logger.Infof("[analyzeClusterAZDistribution] 📊 集群 %s: 运行Pod数=%d, 节点数=%d", clusterID, podInfo.TotalRunningPods, len(podInfo.NodeNames))

	// 分析zone分布
	clusterZones := make(map[string]struct{})
	clusterRegions := make(map[string]struct{})
	hasZoneInfo := false
	hasRegionInfo := false
	noLabelNodes := 0

	// i.logger.Infof("[analyzeClusterAZDistribution] 🏷️  开始分析节点标签，节点数: %d", len(podInfo.NodeNames))

	for _, nodeName := range podInfo.NodeNames {
		if nodeName == "" {
			continue
		}

		if nodeLabel, exists := nodeLabels[nodeName]; exists {
			if nodeLabel.Zone != "" {
				clusterZones[nodeLabel.Zone] = struct{}{}
				hasZoneInfo = true
				// i.logger.Infof("[analyzeClusterAZDistribution] 📍 节点 %s -> AZ: %s", nodeName, nodeLabel.Zone)
			}
			if nodeLabel.Region != "" {
				clusterRegions[nodeLabel.Region] = struct{}{}
				hasRegionInfo = true
			}
		} else {
			noLabelNodes++
			// i.logger.Warnf("[analyzeClusterAZDistribution] ⚠️  节点 %s 未找到标签信息", nodeName)
		}
	}

	// i.logger.Infof("[analyzeClusterAZDistribution] 📊 标签分析结果: 发现%d个AZ, %d个region, %d个节点无标签", len(clusterZones), len(clusterRegions), noLabelNodes)

	// 打印发现的AZ
	if len(clusterZones) > 0 {
		zones := make([]string, 0, len(clusterZones))
		for z := range clusterZones {
			zones = append(zones, z)
		}
		// i.logger.Infof("[analyzeClusterAZDistribution] 🌍 发现的AZ: %v", zones)
	}

	// 如果无法获取zone信息，返回unknown状态
	if !hasZoneInfo && len(podInfo.NodeNames) > 0 {
		// i.logger.Warnf("[analyzeClusterAZDistribution] ⚠️  集群 %s 无法获取zone信息，节点数=%d，无标签节点数=%d", clusterID, len(podInfo.NodeNames), noLabelNodes)
		return i.createUnknownResult(clusterID, componentName, podInfo.TotalRunningPods, "无法查询zone信息")
	}

	// 如果所有节点都没有标签信息，也返回unknown状态
	if noLabelNodes == len(podInfo.NodeNames) && len(podInfo.NodeNames) > 0 {
		// i.logger.Warnf("[analyzeClusterAZDistribution] ⚠️  集群 %s 所有节点都没有标签信息", clusterID)
		return i.createUnknownResult(clusterID, componentName, podInfo.TotalRunningPods, "所有节点都没有标签信息")
	}

	// 获取region alias用于计算标准zone数量
	var regionAlias string
	if hasRegionInfo {
		for r := range clusterRegions {
			regionAlias = r
			break
		}
	}

	// 如果没有从节点标签获取到region，尝试从cluster基本信息中获取
	if regionAlias == "" {
		if clusterInfo, exists := clusterInfoMap[clusterID]; exists && clusterInfo.Region != "" {
			regionAlias = region.GetRegionAlias(clusterInfo.Region)
			// i.logger.Infof("[analyzeClusterAZDistribution] 📍 集群 %s 从cluster表获取region: %s -> %s", clusterID, clusterInfo.Region, regionAlias)
		}
	}

	// 如果仍然无法获取region信息，返回错误
	if regionAlias == "" {
		// i.logger.Warnf("[analyzeClusterAZDistribution] ⚠️  集群 %s 无法获取region信息（节点标签和cluster表都没有）", clusterID)
		return i.createUnknownResult(clusterID, componentName, podInfo.TotalRunningPods, "无法获取region信息")
	}

	// 计算标准zone数量，如果获取失败直接返回错误
	zoneNum := region.GetZoneNumWithFallback(regionAlias)
	if zoneNum <= 0 {
		// i.logger.Warnf("[analyzeClusterAZDistribution] ⚠️  集群 %s 的region %s 无法获取zone数量或zone数量异常: %d", clusterID, regionAlias, zoneNum)
		return i.createUnknownResult(clusterID, componentName, podInfo.TotalRunningPods, fmt.Sprintf("region %s 的zone数量异常: %d", regionAlias, zoneNum))
	}

	// i.logger.Infof("[analyzeClusterAZDistribution] 📍 集群 %s region: %s, 标准zone数量: %d", clusterID, regionAlias, zoneNum)

	// 计算多AZ状态
	uniqueZoneCount := len(clusterZones)
	uniqueNodeCount := len(uniqueStrings(podInfo.NodeNames))
	multinode := uniqueNodeCount > 1

	// i.logger.Infof("[analyzeClusterAZDistribution] 🧮 计算结果: uniqueZoneCount=%d, uniqueNodeCount=%d, zoneNum=%d, runningPods=%d",
	// uniqueZoneCount, uniqueNodeCount, zoneNum, podInfo.TotalRunningPods)

	var multiAZStatus string
	var status string
	var message string

	// 边界情况检查
	if podInfo.TotalRunningPods <= 0 {
		multiAZStatus = "not_deployed"
		status = "not_deployed"
		message = fmt.Sprintf("集群 %s 没有部署 %s 组件", clusterID, componentName)
		// i.logger.Warnf("[analyzeClusterAZDistribution] ⚠️  Pod数量异常: %d", podInfo.TotalRunningPods)
	} else if len(podInfo.NodeNames) == 0 {
		multiAZStatus = "no_nodes"
		status = "unknown"
		message = fmt.Sprintf("集群 %s 的 %s 没有节点信息", clusterID, componentName)
		// i.logger.Warnf("[analyzeClusterAZDistribution] ⚠️  节点信息为空")
	} else if uniqueZoneCount == 0 {
		multiAZStatus = "no_zone_info"
		status = "unknown"
		message = fmt.Sprintf("集群 %s 的 %s 无法获取AZ信息", clusterID, componentName)
		// i.logger.Warnf("[analyzeClusterAZDistribution] ⚠️  无法获取AZ信息，uniqueZoneCount=0")
	} else if podInfo.TotalRunningPods == 1 || zoneNum <= 1 {
		multiAZStatus = "false"
		status = "failed"
		message = fmt.Sprintf("集群 %s 的 %s 未实现多AZ分布", clusterID, componentName)
		// i.logger.Infof("[analyzeClusterAZDistribution] ❌ 失败原因: Pod数量=%d 或 zone数量=%d 不足", podInfo.TotalRunningPods, zoneNum)
	} else if uniqueZoneCount >= min(zoneNum, podInfo.TotalRunningPods) {
		multiAZStatus = "passed"
		status = "passed"
		zones := make([]string, 0, len(clusterZones))
		for z := range clusterZones {
			zones = append(zones, z)
		}
		sort.Strings(zones)
		zonesStr := strings.Join(zones, ", ")
		message = fmt.Sprintf("集群 %s 的 %s 实现了多AZ分布：%d个zone (%s)", clusterID, componentName, uniqueZoneCount, zonesStr)
		// i.logger.Infof("[analyzeClusterAZDistribution] ✅ 通过: 分布在%d个AZ: %s", uniqueZoneCount, zonesStr)
	} else {
		multiAZStatus = "false"
		status = "failed"
		message = fmt.Sprintf("集群 %s 的 %s 未实现多AZ分布", clusterID, componentName)
		// i.logger.Infof("[analyzeClusterAZDistribution] ❌ 失败: 需要至少%d个AZ，但只有%d个", min(zoneNum, podInfo.TotalRunningPods), uniqueZoneCount)
	}

	// 构建zones数组
	zones := make([]string, 0, len(clusterZones))
	for z := range clusterZones {
		zones = append(zones, z)
	}
	sort.Strings(zones)

	// 构建Details
	details := map[string]interface{}{
		"running_pod_count": podInfo.TotalRunningPods,
		"unique_zone_count": uniqueZoneCount,
		"unique_node_count": uniqueNodeCount,
		"multinode":         multinode,
		"zones":             zones,
		"multi_az_status":   multiAZStatus,
	}

	// 如果状态为unknown，添加unknown_reason
	if status == "unknown" {
		switch multiAZStatus {
		case "no_nodes":
			details["unknown_reason"] = fmt.Sprintf("集群 %s 的 %s 没有节点信息", clusterID, componentName)
		case "no_zone_info":
			details["unknown_reason"] = fmt.Sprintf("集群 %s 的 %s 无法获取AZ信息", clusterID, componentName)
		default:
			details["unknown_reason"] = message
		}
	}

	// 如果状态为not_deployed，添加not_deployed_reason
	if status == "not_deployed" {
		details["not_deployed_reason"] = fmt.Sprintf("集群 %s 没有部署 %s 组件", clusterID, componentName)
	}

	return types.InspectionResult{
		InspectorID:   i.inspectorInfo.ID,
		InspectorName: i.inspectorInfo.Name,
		Status:        status,
		Message:       message,
		Details:       details,
	}
}

// createNoPodsResult 创建无Pod的检查结果
func (i *UserClusterBatchMultiAZInspector) createNoPodsResult(clusterID, componentName string) types.InspectionResult {
	// i.logger.Infof("[createNoPodsResult] 集群 %s 的组件 %s 没有运行的Pod", clusterID, componentName)
	return types.InspectionResult{
		InspectorID:   i.inspectorInfo.ID,
		InspectorName: i.inspectorInfo.Name,
		Status:        "not_deployed", // 改为not_deployed状态
		Message:       fmt.Sprintf("集群 %s 没有部署 %s 组件", clusterID, componentName),
		Details: map[string]interface{}{
			"running_pod_count":   0,
			"unique_zone_count":   0,
			"unique_node_count":   0,
			"multinode":           false,
			"zones":               []string{},
			"multi_az_status":     "not_deployed",
			"not_deployed_reason": fmt.Sprintf("集群 %s 没有部署 %s 组件", clusterID, componentName),
		},
	}
}

// createUnknownResult 创建未知状态的检查结果
func (i *UserClusterBatchMultiAZInspector) createUnknownResult(clusterID, componentName string, runningPods int, reason string) types.InspectionResult {
	return types.InspectionResult{
		InspectorID:   i.inspectorInfo.ID,
		InspectorName: i.inspectorInfo.Name,
		Status:        "unknown",
		Message:       fmt.Sprintf("集群 %s 的 %s 状态未知: %s", clusterID, componentName, reason),
		Details: map[string]interface{}{
			"running_pod_count": runningPods,
			"unique_zone_count": 0,
			"unique_node_count": 0,
			"multinode":         false,
			"zones":             []string{},
			"multi_az_status":   "unknown",
			"unknown_reason":    reason, // 添加unknown_reason字段
		},
	}
}

// 辅助函数：去重字符串数组
func uniqueStrings(arr []string) []string {
	m := make(map[string]struct{})
	for _, v := range arr {
		if v != "" {
			m[v] = struct{}{}
		}
	}
	res := make([]string, 0, len(m))
	for k := range m {
		res = append(res, k)
	}
	return res
}

// 辅助函数：取最小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
