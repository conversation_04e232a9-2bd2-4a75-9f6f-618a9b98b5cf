package output

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/sirupsen/logrus"
	"gopkg.in/yaml.v3"
)

// OutputFormatter 输出格式化器
type OutputFormatter struct {
	logger *logrus.Logger
}

func NewOutputFormatter(logger *logrus.Logger) *OutputFormatter {
	return &OutputFormatter{
		logger: logger,
	}
}

func (o *OutputFormatter) FormatClusterOutput(results []map[string]interface{}) interface{} {
	clusterData := make(map[string]map[string]interface{})

	for _, result := range results {
		details, _ := result["details"].(map[string]interface{})
		workloadInfo, _ := result["workload_info"].(map[string]interface{})
		clusterMetadata, _ := result["cluster_metadata"].(map[string]interface{})

		clusterID, _ := clusterMetadata["cluster_id"].(string)
		if clusterID == "" {
			continue
		}

		workloadName, _ := workloadInfo["workload_name"].(string)
		if workloadName == "" {
			continue
		}

		componentType, _ := workloadInfo["component_type"].(string)
		if componentType == "" {
			componentType = "user_cluster" // 默认为用户集群组件
		}

		// 初始化集群数据
		if _, exists := clusterData[clusterID]; !exists {
			clusterData[clusterID] = map[string]interface{}{
				"cluster_id":              clusterID,
				"cluster_metadata":        clusterMetadata,
				"user_cluster_components": make(map[string]interface{}),
				"meta_cluster_components": make(map[string]interface{}),
			}
		}

		// 根据组件类型决定deployment_location和存储位置
		var deploymentLocation string
		var componentContainer map[string]interface{}
		if componentType == "meta_cluster" || componentType == "meta" {
			deploymentLocation = "Meta"
			componentContainer = clusterData[clusterID]["meta_cluster_components"].(map[string]interface{})
		} else {
			deploymentLocation = fmt.Sprintf("集群 %s", clusterID)
			componentContainer = clusterData[clusterID]["user_cluster_components"].(map[string]interface{})
		}

		// 初始化组件数据
		if _, exists := componentContainer[workloadName]; !exists {
			componentContainer[workloadName] = map[string]interface{}{
				"workload_metadata": map[string]interface{}{
					"component_name":      workloadInfo["component_name"],
					"workload_name":       workloadName,
					"deployment_location": deploymentLocation,
					"priority":            workloadInfo["priority"],
				},
				"inspections": make(map[string]interface{}),
			}
		}

		// 添加检查结果
		workload := componentContainer[workloadName].(map[string]interface{})
		inspections := workload["inspections"].(map[string]interface{})
		inspectorID, _ := result["inspector_id"].(string)
		if inspectorID == "" {
			inspectorID = "unknown"
		}

		inspections[inspectorID] = map[string]interface{}{
			"inspector_name": result["inspector_name"],
			"status":         result["status"],
			"message":        result["message"],
			"details":        details,
		}
	}

	// 转换为数组格式
	var clusterArray []map[string]interface{}
	for _, cluster := range clusterData {
		clusterArray = append(clusterArray, cluster)
	}

	return clusterArray
}

func (o *OutputFormatter) SaveJSON(data interface{}, filePath string, prettyPrint bool) error {
	if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
		return fmt.Errorf("创建输出目录失败: %w", err)
	}

	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建JSON文件失败: %w", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	if prettyPrint {
		encoder.SetIndent("", "  ")
	}

	if err := encoder.Encode(data); err != nil {
		return fmt.Errorf("写入JSON文件失败: %w", err)
	}

	o.logger.Infof("结果已保存到JSON文件: %s", filePath)
	return nil
}

func (o *OutputFormatter) SaveYAML(data interface{}, filePath string) error {
	if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
		return fmt.Errorf("创建输出目录失败: %w", err)
	}

	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建YAML文件失败: %w", err)
	}
	defer file.Close()

	encoder := yaml.NewEncoder(file)
	encoder.SetIndent(2)
	if err := encoder.Encode(data); err != nil {
		return fmt.Errorf("写入YAML文件失败: %w", err)
	}

	o.logger.Infof("结果已保存到YAML文件: %s", filePath)
	return nil
}

// PrintConsoleSummary 打印控制台摘要 (已废弃，使用cmd/main.go中的outputConsole)
// 保留此函数仅为向后兼容，实际不再使用
func (o *OutputFormatter) PrintConsoleSummary(results []map[string]interface{}) {
	fmt.Println("注意：此函数已废弃，请使用cmd/main.go中的outputConsole函数")
	fmt.Printf("收到 %d 个结果，但不会处理\n", len(results))
}

// GenerateReport 生成报告
func (o *OutputFormatter) GenerateReport(results []map[string]interface{}, reportPath string) error {
	clusterOutput := o.FormatClusterOutput(results)

	report := map[string]interface{}{
		"scan_time":     time.Now().Format("2006-01-02 15:04:05"),
		"total_results": len(results),
		"clusters":      clusterOutput,
	}

	return o.SaveJSON(report, reportPath, true)
}

// GetSummaryStats 获取统计摘要
func (o *OutputFormatter) GetSummaryStats(results []map[string]interface{}) map[string]interface{} {
	stats := map[string]interface{}{
		"total_results": 0,
		"passed":        0,
		"failed":        0,
		"unknown":       0,
		"by_cluster":    make(map[string]int),
		"by_inspector":  make(map[string]int),
	}

	for _, result := range results {
		stats["total_results"] = stats["total_results"].(int) + 1

		status, _ := result["status"].(string)
		switch status {
		case "passed":
			stats["passed"] = stats["passed"].(int) + 1
		case "failed":
			stats["failed"] = stats["failed"].(int) + 1
		default:
			stats["unknown"] = stats["unknown"].(int) + 1
		}

		if details, ok := result["details"].(map[string]interface{}); ok {
			if clusterID, ok := details["cluster_id"].(string); ok {
				byCluster := stats["by_cluster"].(map[string]int)
				byCluster[clusterID]++
			}
		}

		if inspectorName, ok := result["inspector_name"].(string); ok {
			byInspector := stats["by_inspector"].(map[string]int)
			byInspector[inspectorName]++
		}
	}

	return stats
}

// ExportToMultipleFormats 导出到多种格式
func (o *OutputFormatter) ExportToMultipleFormats(results []map[string]interface{}, baseDir string) error {
	clusterOutput := o.FormatClusterOutput(results)
	clusterArray := clusterOutput.([]map[string]interface{})

	// 添加扫描日期和时间戳
	now := time.Now()
	dateStr := now.Format("2006-01-02")
	timestampStr := now.Format("2006-01-02T15:04:05.000000")

	// 为每个集群添加时间戳
	for _, cluster := range clusterArray {
		// 重新排序字段，确保 scan_date 和 timestamp 在最后
		orderedCluster := make(map[string]interface{})
		// 按指定顺序添加字段
		orderedCluster["cluster_id"] = cluster["cluster_id"]
		orderedCluster["cluster_metadata"] = cluster["cluster_metadata"]
		orderedCluster["user_cluster_components"] = cluster["user_cluster_components"]
		orderedCluster["meta_cluster_components"] = cluster["meta_cluster_components"]
		orderedCluster["scan_date"] = dateStr
		orderedCluster["timestamp"] = timestampStr

		// 复制回原来的cluster
		for k := range cluster {
			delete(cluster, k)
		}
		for k, v := range orderedCluster {
			cluster[k] = v
		}
	}

	// 导出JSON
	jsonPath := filepath.Join(baseDir, "scan_results.json")
	if err := o.SaveJSON(clusterArray, jsonPath, true); err != nil {
		return fmt.Errorf("导出JSON失败: %w", err)
	}

	// 导出YAML
	yamlPath := filepath.Join(baseDir, "scan_results.yaml")
	if err := o.SaveYAML(clusterArray, yamlPath); err != nil {
		return fmt.Errorf("导出YAML失败: %w", err)
	}

	return nil
}
