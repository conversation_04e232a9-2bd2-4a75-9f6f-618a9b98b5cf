package cache

import (
	"fmt"
	"sync"
	"time"

	"cmdb-scanner/internal/config"
	"cmdb-scanner/internal/database"
	"cmdb-scanner/internal/types"

	"github.com/sirupsen/logrus"
)

// DataCacheManager 数据缓存管理器
type DataCacheManager struct {
	mysqlClient      *database.MySQLClient
	clickhouseClient *database.ClickHouseClient
	config           *config.Config
	scanDate         time.Time
	logger           *logrus.Logger

	// 缓存数据
	metaDataCache map[string]*types.MetaComponentDataBatch // workloadName -> data
	podDataCache  map[string]*types.PodDataBatch           // workloadName -> data
	cacheMutex    sync.RWMutex

	// 性能统计
	queryStats map[string]int // 查询统计
	statsMutex sync.Mutex
}

// NewDataCacheManager 创建数据缓存管理器
func NewDataCacheManager(
	mysqlClient *database.MySQLClient,
	clickhouseClient *database.ClickHouseClient,
	config *config.Config,
	scanDate time.Time,
	logger *logrus.Logger,
) *DataCacheManager {
	return &DataCacheManager{
		mysqlClient:      mysqlClient,
		clickhouseClient: clickhouseClient,
		config:           config,
		scanDate:         scanDate,
		logger:           logger,
		metaDataCache:    make(map[string]*types.MetaComponentDataBatch),
		podDataCache:     make(map[string]*types.PodDataBatch),
		queryStats:       make(map[string]int),
	}
}

// PreloadAllComponentData 预加载所有组件数据
func (c *DataCacheManager) PreloadAllComponentData(clusterIDs []string) error {
	c.logger.Infof("🚀 [DataCacheManager] 开始预加载所有组件数据，集群数量: %d", len(clusterIDs))
	startTime := time.Now()

	// 并行预加载用户组件和Meta组件数据
	var wg sync.WaitGroup
	errChan := make(chan error, 2)

	// 预加载用户组件数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := c.preloadUserComponentData(clusterIDs); err != nil {
			errChan <- fmt.Errorf("预加载用户组件数据失败: %w", err)
		}
	}()

	// 预加载Meta组件数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := c.preloadMetaComponentData(clusterIDs); err != nil {
			errChan <- fmt.Errorf("预加载Meta组件数据失败: %w", err)
		}
	}()

	wg.Wait()
	close(errChan)

	// 检查错误
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	duration := time.Since(startTime)
	c.logger.Infof("✅ [DataCacheManager] 预加载完成，耗时: %v", duration)
	c.printCacheStats()

	return nil
}

// preloadUserComponentData 预加载用户组件数据
func (c *DataCacheManager) preloadUserComponentData(clusterIDs []string) error {
	c.logger.Infof("📊 [DataCacheManager] 预加载用户组件数据")

	// 获取所有启用的用户组件
	var workloads []string
	for _, component := range c.config.UserClusterComponents {
		if component.Enabled {
			workloads = append(workloads, component.WorkloadName)
		}
	}

	if len(workloads) == 0 {
		c.logger.Infof("📋 [DataCacheManager] 没有启用的用户组件")
		return nil
	}

	// 并行查询所有用户组件的Pod数据
	var wg sync.WaitGroup
	errChan := make(chan error, len(workloads))

	for _, workload := range workloads {
		wg.Add(1)
		go func(workloadName string) {
			defer wg.Done()

			c.logger.Debugf("🔍 [DataCacheManager] 查询用户组件Pod数据: %s", workloadName)
			podData, err := c.clickhouseClient.BatchQueryPodData(clusterIDs, workloadName, c.scanDate)
			if err != nil {
				errChan <- fmt.Errorf("查询用户组件 %s Pod数据失败: %w", workloadName, err)
				return
			}

			c.cacheMutex.Lock()
			c.podDataCache[workloadName] = podData
			c.cacheMutex.Unlock()

			c.incrementQueryStats("user_pod_query")
			c.logger.Debugf("✅ [DataCacheManager] 用户组件 %s Pod数据缓存完成", workloadName)
		}(workload)
	}

	wg.Wait()
	close(errChan)

	// 检查错误
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	c.logger.Infof("✅ [DataCacheManager] 用户组件数据预加载完成，组件数量: %d", len(workloads))
	return nil
}

// preloadMetaComponentData 预加载Meta组件数据
func (c *DataCacheManager) preloadMetaComponentData(clusterIDs []string) error {
	c.logger.Infof("📊 [DataCacheManager] 预加载Meta组件数据")

	// 获取所有启用的Meta组件
	var workloads []string
	var fuzzySearchMap = make(map[string]bool)

	for _, component := range c.config.MetaClusterComponents {
		if component.Enabled {
			workloads = append(workloads, component.WorkloadName)
			fuzzySearchMap[component.WorkloadName] = component.FuzzySearch
		}
	}

	if len(workloads) == 0 {
		c.logger.Infof("📋 [DataCacheManager] 没有启用的Meta组件")
		return nil
	}

	// 并行查询所有Meta组件数据
	var wg sync.WaitGroup
	errChan := make(chan error, len(workloads))

	for _, workload := range workloads {
		wg.Add(1)
		go func(workloadName string) {
			defer wg.Done()

			fuzzySearch := fuzzySearchMap[workloadName]
			c.logger.Debugf("🔍 [DataCacheManager] 查询Meta组件数据: %s (模糊查询: %t)", workloadName, fuzzySearch)

			metaData, err := c.mysqlClient.BatchQueryMetaComponentData(clusterIDs, workloadName, c.scanDate, fuzzySearch)
			if err != nil {
				errChan <- fmt.Errorf("查询Meta组件 %s 数据失败: %w", workloadName, err)
				return
			}

			c.cacheMutex.Lock()
			c.metaDataCache[workloadName] = metaData
			c.cacheMutex.Unlock()

			c.incrementQueryStats("meta_component_query")
			c.logger.Debugf("✅ [DataCacheManager] Meta组件 %s 数据缓存完成", workloadName)
		}(workload)
	}

	wg.Wait()
	close(errChan)

	// 检查错误
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	c.logger.Infof("✅ [DataCacheManager] Meta组件数据预加载完成，组件数量: %d", len(workloads))
	return nil
}

// GetMetaComponentData 获取Meta组件数据（从缓存）
func (c *DataCacheManager) GetMetaComponentData(workloadName string) (*types.MetaComponentDataBatch, error) {
	c.cacheMutex.RLock()
	defer c.cacheMutex.RUnlock()

	data, exists := c.metaDataCache[workloadName]
	if !exists {
		return nil, fmt.Errorf("Meta组件 %s 数据未缓存", workloadName)
	}

	c.incrementQueryStats("meta_cache_hit")
	return data, nil
}

// GetPodData 获取Pod数据（从缓存）
func (c *DataCacheManager) GetPodData(workloadName string) (*types.PodDataBatch, error) {
	c.cacheMutex.RLock()
	defer c.cacheMutex.RUnlock()

	data, exists := c.podDataCache[workloadName]
	if !exists {
		return nil, fmt.Errorf("用户组件 %s Pod数据未缓存", workloadName)
	}

	c.incrementQueryStats("pod_cache_hit")
	return data, nil
}

// incrementQueryStats 增加查询统计
func (c *DataCacheManager) incrementQueryStats(statType string) {
	c.statsMutex.Lock()
	defer c.statsMutex.Unlock()
	c.queryStats[statType]++
}

// printCacheStats 打印缓存统计信息
func (c *DataCacheManager) printCacheStats() {
	c.statsMutex.Lock()
	defer c.statsMutex.Unlock()

	c.logger.Infof("📈 [DataCacheManager] 缓存统计:")
	c.logger.Infof("  Meta组件缓存数量: %d", len(c.metaDataCache))
	c.logger.Infof("  Pod数据缓存数量: %d", len(c.podDataCache))

	for statType, count := range c.queryStats {
		c.logger.Infof("  %s: %d", statType, count)
	}
}

// GetCacheStats 获取缓存统计信息
func (c *DataCacheManager) GetCacheStats() map[string]interface{} {
	c.statsMutex.Lock()
	defer c.statsMutex.Unlock()

	stats := make(map[string]interface{})
	stats["meta_cache_count"] = len(c.metaDataCache)
	stats["pod_cache_count"] = len(c.podDataCache)

	for statType, count := range c.queryStats {
		stats[statType] = count
	}

	return stats
}
