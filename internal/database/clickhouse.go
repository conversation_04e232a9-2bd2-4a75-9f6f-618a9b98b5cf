package database

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"cmdb-scanner/internal/config"
	"cmdb-scanner/internal/types"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/sirupsen/logrus"
)

type ClickHouseClient struct {
	conn clickhouse.Conn
}

func NewClickHouseClient(cfg *config.ClickHouseConfig) (*ClickHouseClient, error) {
	logrus.Infof("[NewClickHouseClient] 🔌 开始创建ClickHouse客户端")
	logrus.Infof("[NewClickHouseClient] 🌐 连接地址: %s:%s", cfg.Host, cfg.Port)

	// 构建连接字符串
	dsn := fmt.Sprintf("clickhouse://%s:%s@%s:%s/%s",
		cfg.Username, cfg.Password, cfg.Host, cfg.Port, cfg.Database)

	// 添加连接参数
	params := []string{}
	if cfg.MaxConnections > 0 {
		params = append(params, fmt.Sprintf("max_open_conns=%d", cfg.MaxConnections))
	}
	if cfg.ConnectTimeout != "" {
		params = append(params, fmt.Sprintf("connect_timeout=%s", cfg.ConnectTimeout))
	}

	if len(params) > 0 {
		dsn += "?" + strings.Join(params, "&")
	}

	logrus.Debugf("[NewClickHouseClient] 🔗 DSN: %s", dsn)

	// 创建连接
	conn, err := clickhouse.Open(&clickhouse.Options{
		Addr: []string{fmt.Sprintf("%s:%s", cfg.Host, cfg.Port)},
		Auth: clickhouse.Auth{
			Database: cfg.Database,
			Username: cfg.Username,
			Password: cfg.Password,
		},
	})

	if err != nil {
		logrus.Errorf("[NewClickHouseClient] ❌ 连接失败: %v", err)
		return nil, fmt.Errorf("连接ClickHouse失败: %w", err)
	}

	// 测试连接
	if err := conn.Ping(context.Background()); err != nil {
		logrus.Errorf("[NewClickHouseClient] ❌ 连接测试失败: %v", err)
		return nil, fmt.Errorf("ClickHouse连接测试失败: %w", err)
	}

	logrus.Infof("[NewClickHouseClient] ✅ ClickHouse客户端创建成功")
	return &ClickHouseClient{conn: conn}, nil
}

func (c *ClickHouseClient) ExecuteQuery(ctx context.Context, query string, args ...interface{}) ([]map[string]interface{}, error) {
	// logrus.Debugf("[ExecuteQuery] 🚀 开始执行查询")
	// logrus.Debugf("[ExecuteQuery] 📝 SQL: %s", query)
	// logrus.Debugf("[ExecuteQuery] 📋 参数: %v", args)

	rows, err := c.conn.Query(ctx, query, args...)
	if err != nil {
		// logrus.Errorf("[ExecuteQuery] ❌ 查询执行失败: %v", err)
		return nil, fmt.Errorf("查询执行失败: %w", err)
	}
	defer rows.Close()

	var results []map[string]interface{}
	columnTypes := rows.ColumnTypes()
	columnNames := make([]string, len(columnTypes))
	for i, ct := range columnTypes {
		columnNames[i] = ct.Name()
	}

	for rows.Next() {
		// 为每个列创建适当的类型指针
		values := make([]interface{}, len(columnNames))
		valuePtrs := make([]interface{}, len(columnNames))

		// 根据列类型创建适当的变量
		for i, ct := range columnTypes {
			switch ct.DatabaseTypeName() {
			case "String", "FixedString":
				var s string
				values[i] = &s
				valuePtrs[i] = &s
			case "UInt64":
				var u uint64
				values[i] = &u
				valuePtrs[i] = &u
			case "UInt32":
				var u uint32
				values[i] = &u
				valuePtrs[i] = &u
			case "Int64":
				var n int64
				values[i] = &n
				valuePtrs[i] = &n
			case "Int32":
				var n int32
				values[i] = &n
				valuePtrs[i] = &n
			case "Float64":
				var f float64
				values[i] = &f
				valuePtrs[i] = &f
			case "Date", "DateTime":
				var t time.Time
				values[i] = &t
				valuePtrs[i] = &t
			case "Array(String)":
				var arr []string
				values[i] = &arr
				valuePtrs[i] = &arr
			default:
				// 对于未知类型，使用string作为默认类型
				var s string
				values[i] = &s
				valuePtrs[i] = &s
			}
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			// logrus.Errorf("[ExecuteQuery] ❌ 扫描行失败: %v", err)
			continue
		}

		row := make(map[string]interface{})
		for i, colName := range columnNames {
			// 解引用指针获取实际值
			switch v := values[i].(type) {
			case *string:
				row[colName] = *v
			case *uint64:
				row[colName] = *v
			case *uint32:
				row[colName] = *v
			case *int64:
				row[colName] = *v
			case *int32:
				row[colName] = *v
			case *float64:
				row[colName] = *v
			case *time.Time:
				row[colName] = *v
			case *[]string:
				row[colName] = *v
			default:
				row[colName] = v
			}
		}
		results = append(results, row)
	}

	if err := rows.Err(); err != nil {
		logrus.Errorf("[ExecuteQuery] ❌ 行迭代错误: %v", err)
		return nil, fmt.Errorf("行迭代错误: %w", err)
	}

	// logrus.Debugf("[ExecuteQuery] ✅ 查询完成，返回 %d 行", len(results))
	return results, nil
}

// Close 关闭连接
func (c *ClickHouseClient) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}

// ==================== 批量查询方法 ====================

// GetAllClusterIDs 获取所有相关的cluster_id（去重）
func (c *ClickHouseClient) GetAllClusterIDs(date time.Time) ([]string, error) {
	logrus.Infof("[GetAllClusterIDs] 🔍 获取所有cluster_id，日期: %s", date.Format("2006-01-02"))
	query := `SELECT DISTINCT ClusterId FROM cluster WHERE DateDay = ? and ClusterId = ''`
	results, err := c.ExecuteQuery(context.Background(), query, date.Format("2006-01-02"))
	if err != nil {
		// logrus.Errorf("[GetAllClusterIDs] ❌ 查询失败: %v", err)
		return nil, fmt.Errorf("查询cluster_id失败: %w", err)
	}

	var clusterIDs []string
	for _, row := range results {
		if clusterID, ok := row["ClusterId"].(string); ok && clusterID != "" {
			clusterIDs = append(clusterIDs, clusterID)
		}
	}

	logrus.Infof("[GetAllClusterIDs] ✅ 获取到 %d 个cluster_id", len(clusterIDs))
	return clusterIDs, nil
}

// GetClusterMetadataBatch 批量查询集群基础信息
func (c *ClickHouseClient) GetClusterMetadataBatch(clusterIDs []string, date time.Time) (map[string]types.ClusterMetadata, error) {
	if len(clusterIDs) == 0 {
		return make(map[string]types.ClusterMetadata), nil
	}

	logrus.Debugf("[GetClusterMetadataBatch] 🔍 批量查询集群基础信息，集群数量: %d", len(clusterIDs))

	// 构建IN子句的占位符
	placeholders := make([]string, len(clusterIDs))
	for i := range placeholders {
		placeholders[i] = "?"
	}

	query := fmt.Sprintf(`SELECT ClusterId, ClusterType, Region, AppId, Version, Status
		FROM cluster 
		WHERE DateDay = ? AND ClusterId IN (%s)`, strings.Join(placeholders, ","))

	// 构建查询参数
	args := []interface{}{date.Format("2006-01-02")}
	for _, clusterID := range clusterIDs {
		args = append(args, clusterID)
	}

	results, err := c.ExecuteQuery(context.Background(), query, args...)
	if err != nil {
		// logrus.Errorf("[GetClusterMetadataBatch] ❌ 查询失败: %v", err)
		return nil, fmt.Errorf("批量查询集群信息失败: %w", err)
	}

	clusterMetadata := make(map[string]types.ClusterMetadata)
	for _, row := range results {
		clusterID := getString(row, "ClusterId")
		if clusterID == "" {
			continue
		}

		appID := uint64(0)
		if appIDRaw, ok := row["AppId"]; ok {
			if appIDUint64, ok := appIDRaw.(uint64); ok {
				appID = appIDUint64
			}
		}

		metadata := types.ClusterMetadata{
			ClusterID:   clusterID,
			ClusterName: getString(row, "Name"),
			ClusterType: getString(row, "ClusterType"),
			Region:      getString(row, "Region"),
			AppID:       appID,
			Version:     getString(row, "Version"),
			Status:      getString(row, "Status"),
			DataSource:  "clickhouse",
		}

		clusterMetadata[clusterID] = metadata
	}

	logrus.Debugf("[GetClusterMetadataBatch] ✅ 查询到 %d 个集群的基础信息", len(clusterMetadata))
	return clusterMetadata, nil
}

// BatchQueryPodData 批量查询Pod数据
func (c *ClickHouseClient) BatchQueryPodData(clusterIDs []string, workloadName string, date time.Time) (*types.PodDataBatch, error) {
	if len(clusterIDs) == 0 {
		return &types.PodDataBatch{ClusterPodData: make(map[string]*types.ClusterPodInfo)}, nil
	}

	// logrus.Debugf("[BatchQueryPodData] 🔍 批量查询Pod数据，集群数量: %d, workload: %s", len(clusterIDs), workloadName)

	// 构建IN子句的占位符
	placeholders := make([]string, len(clusterIDs))
	for i := range placeholders {
		placeholders[i] = "?"
	}

	query := fmt.Sprintf(`SELECT 
		ClusterId,
		groupArray(NodeName) AS all_node_names,
		sum(running_count) AS total_running_pods_for_cluster
	FROM (
		SELECT 
			ClusterId,
			NodeName,
			countIf(JSONExtractString(Data, 'status', 'phase') = 'Running') AS running_count
		FROM pod 
		WHERE DateDay = ? AND ClusterId IN (%s) AND WorkloadName = ?
		GROUP BY ClusterId, NodeName
	)
	GROUP BY ClusterId`, strings.Join(placeholders, ","))

	// 构建查询参数
	args := []interface{}{date.Format("2006-01-02")}
	for _, clusterID := range clusterIDs {
		args = append(args, clusterID)
	}
	args = append(args, workloadName)

	results, err := c.ExecuteQuery(context.Background(), query, args...)
	if err != nil {
		logrus.Errorf("[BatchQueryPodData] ❌ 查询失败: %v", err)
		return nil, fmt.Errorf("批量查询Pod数据失败: %w", err)
	}

	podDataBatch := &types.PodDataBatch{
		ClusterPodData: make(map[string]*types.ClusterPodInfo),
	}

	// 处理查询结果
	for _, row := range results {
		clusterID := getString(row, "ClusterId")
		if clusterID == "" {
			continue
		}

		// 处理节点名称数组
		var nodeNames []string
		if nodeNamesRaw, ok := row["all_node_names"]; ok {
			// 尝试直接断言为 []string 类型
			if stringArray, ok := nodeNamesRaw.([]string); ok {
				// 如果是 []string，直接赋值
				nodeNames = stringArray
			} else if interfaceArray, ok := nodeNamesRaw.([]interface{}); ok {
				// 如果是 []interface{}，则遍历转换
				for _, nodeName := range interfaceArray {
					if nodeNameStr, ok := nodeName.(string); ok && nodeNameStr != "" {
						nodeNames = append(nodeNames, nodeNameStr)
					}
				}
			} else { // 可以添加一个else块来处理其他意料之外的类型，进行日志记录
				logrus.Warnf("all_node_names 字段类型未知: %T", nodeNamesRaw)
			}
		}

		// 处理运行Pod数量
		totalRunningPods := 0
		if totalPodsRaw, ok := row["total_running_pods_for_cluster"]; ok {
			if totalPodsUint64, ok := totalPodsRaw.(uint64); ok {
				totalRunningPods = int(totalPodsUint64)
			}
		}

		podDataBatch.ClusterPodData[clusterID] = &types.ClusterPodInfo{
			ClusterID:        clusterID,
			NodeNames:        nodeNames,
			TotalRunningPods: totalRunningPods,
		}
	}

	// 为没有Pod数据的集群创建空记录
	for _, clusterID := range clusterIDs {
		if _, exists := podDataBatch.ClusterPodData[clusterID]; !exists {
			podDataBatch.ClusterPodData[clusterID] = &types.ClusterPodInfo{
				ClusterID:        clusterID,
				NodeNames:        []string{},
				TotalRunningPods: 0,
			}
		}
	}

	logrus.Debugf("[BatchQueryPodData] ✅ 查询到 %d 个集群的Pod数据", len(podDataBatch.ClusterPodData))
	return podDataBatch, nil
}

// BatchQueryNodeLabels 批量查询节点标签
func (c *ClickHouseClient) BatchQueryNodeLabels(nodeNames []string, date time.Time) (map[string]types.NodeLabels, error) {
	if len(nodeNames) == 0 {
		return make(map[string]types.NodeLabels), nil
	}

	// logrus.Debugf("[BatchQueryNodeLabels] 🔍 批量查询节点标签，节点数量: %d", len(nodeNames))

	allResults := make(map[string]types.NodeLabels)
	batchSize := 500 // 每批查询500个节点

	for i := 0; i < len(nodeNames); i += batchSize {
		end := i + batchSize
		if end > len(nodeNames) {
			end = len(nodeNames)
		}
		batch := nodeNames[i:end]

		// 构建IN子句的占位符
		placeholders := make([]string, len(batch))
		for j := range placeholders {
			placeholders[j] = "?"
		}

		query := fmt.Sprintf(`SELECT Name, JSONExtractRaw(Data, 'metadata', 'labels') as Labels
			FROM node 
			WHERE DateDay = ? AND Name IN (%s)`, strings.Join(placeholders, ","))

		// 构建查询参数
		args := []interface{}{date.Format("2006-01-02")}
		for _, nodeName := range batch {
			args = append(args, nodeName)
		}

		results, err := c.ExecuteQuery(context.Background(), query, args...)
		if err != nil {
			// logrus.Errorf("[BatchQueryNodeLabels] ❌ 批次查询失败: %v", err)
			return nil, fmt.Errorf("批量查询节点标签失败: %w", err)
		}

		// 处理查询结果
		for _, row := range results {
			nodeName := getString(row, "Name")
			if nodeName == "" {
				continue
			}

			nodeLabels := types.NodeLabels{
				NodeName: nodeName,
				Labels:   make(map[string]interface{}),
				Zone:     "",
				Region:   "",
			}

			// 解析标签JSON
			if labelsRaw, ok := row["Labels"]; ok {
				if labelsStr, ok := labelsRaw.(string); ok && labelsStr != "" {
					// 解析JSON标签
					nodeLabels.Region, nodeLabels.Zone = parseNodeLabels(labelsStr)
				}
			}

			allResults[nodeName] = nodeLabels
		}

		logrus.Debugf("[BatchQueryNodeLabels] 处理批次 %d-%d，获得 %d 个节点标签", i, end-1, len(results))
	}

	logrus.Debugf("[BatchQueryNodeLabels] ✅ 总共查询到 %d 个节点的标签信息", len(allResults))
	return allResults, nil
}

// ==================== 辅助函数 ====================

func getString(m map[string]interface{}, key string) string {
	if m == nil {
		return ""
	}
	if v, ok := m[key]; ok && v != nil {
		if s, ok := v.(string); ok {
			return s
		}
	}
	return ""
}

// parseNodeLabels 解析节点标签，提取region和zone信息
func parseNodeLabels(labelsStr string) (region, zone string) {
	logrus.Debugf("[parseNodeLabels] 开始解析节点标签")

	// 存储找到的region和zone候选值
	var regionCandidates []string
	var zoneCandidates []string
	var numericZoneCandidates []string

	// 尝试解析为JSON
	var labels map[string]interface{}
	if err := json.Unmarshal([]byte(labelsStr), &labels); err != nil {
		logrus.Debugf("[parseNodeLabels] JSON解析失败，尝试简单字符串解析: %v", err)
		// 如果JSON解析失败，使用简单的字符串解析
		return parseNodeLabelsFromString(labelsStr)
	}

	// 遍历JSON标签
	for key, value := range labels {
		valueStr, ok := value.(string)
		if !ok {
			continue
		}

		logrus.Debugf("[parseNodeLabels] 处理标签: %s = %s", key, valueStr)

		// 提取region信息：查找键后缀为"region"的
		if strings.HasSuffix(key, "region") {
			regionCandidates = append(regionCandidates, valueStr)
			logrus.Debugf("[parseNodeLabels] 找到region候选: %s = %s", key, valueStr)
		}

		// 提取zone信息：查找键后缀为"zone"的
		if strings.HasSuffix(key, "zone") {
			logrus.Debugf("[parseNodeLabels] 找到zone候选: %s = %s", key, valueStr)

			// 检查是否是类似"ap-guangzhou-6"的格式（包含字母和连字符）
			if strings.Contains(valueStr, "-") && containsLetter(valueStr) {
				zoneCandidates = append(zoneCandidates, valueStr)
			} else if isNumericString(valueStr) {
				// 纯数字格式如"100006"
				numericZoneCandidates = append(numericZoneCandidates, valueStr)
			}
		}
	}

	// 选择最佳的region值（取第一个找到的）
	if len(regionCandidates) > 0 {
		region = regionCandidates[0]
		logrus.Debugf("[parseNodeLabels] 选择region: %s", region)
	}

	// 选择最佳的zone值：优先选择带连字符的格式
	if len(zoneCandidates) > 0 {
		zone = zoneCandidates[0]
		logrus.Debugf("[parseNodeLabels] 选择zone (优先格式): %s", zone)
	} else if len(numericZoneCandidates) > 0 {
		zone = numericZoneCandidates[0]
		logrus.Debugf("[parseNodeLabels] 选择zone (数字格式): %s", zone)
	}

	logrus.Debugf("[parseNodeLabels] 解析结果 - region: %s, zone: %s", region, zone)
	return region, zone
}

// parseNodeLabelsFromString 从字符串解析节点标签（备用方法）
func parseNodeLabelsFromString(labelsStr string) (region, zone string) {
	logrus.Debugf("[parseNodeLabelsFromString] 使用字符串解析方法")

	// 存储找到的region和zone候选值
	var regionCandidates []string
	var zoneCandidates []string
	var numericZoneCandidates []string

	// 按逗号分割标签对
	pairs := strings.Split(labelsStr, ",")

	for _, pair := range pairs {
		// 清理空格和引号
		pair = strings.TrimSpace(pair)
		pair = strings.Trim(pair, "{}")

		// 分割键值对
		if strings.Contains(pair, ":") {
			parts := strings.SplitN(pair, ":", 2)
			if len(parts) != 2 {
				continue
			}

			key := strings.TrimSpace(strings.Trim(parts[0], "\""))
			value := strings.TrimSpace(strings.Trim(parts[1], "\""))

			// 提取region信息：查找键后缀为"region"的
			if strings.HasSuffix(key, "region") {
				regionCandidates = append(regionCandidates, value)
				logrus.Debugf("[parseNodeLabelsFromString] 找到region候选: %s = %s", key, value)
			}

			// 提取zone信息：查找键后缀为"zone"的
			if strings.HasSuffix(key, "zone") {
				logrus.Debugf("[parseNodeLabelsFromString] 找到zone候选: %s = %s", key, value)

				// 检查是否是类似"ap-guangzhou-6"的格式（包含字母和连字符）
				if strings.Contains(value, "-") && containsLetter(value) {
					zoneCandidates = append(zoneCandidates, value)
				} else if isNumericString(value) {
					// 纯数字格式如"100006"
					numericZoneCandidates = append(numericZoneCandidates, value)
				}
			}
		}
	}

	// 选择最佳的region值（取第一个找到的）
	if len(regionCandidates) > 0 {
		region = regionCandidates[0]
	}

	// 选择最佳的zone值：优先选择带连字符的格式
	if len(zoneCandidates) > 0 {
		zone = zoneCandidates[0]
	} else if len(numericZoneCandidates) > 0 {
		zone = numericZoneCandidates[0]
	}

	return region, zone
}

// containsLetter 检查字符串是否包含字母
func containsLetter(s string) bool {
	for _, r := range s {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') {
			return true
		}
	}
	return false
}

// isNumericString 检查字符串是否为纯数字
func isNumericString(s string) bool {
	if s == "" {
		return false
	}
	for _, r := range s {
		if r < '0' || r > '9' {
			return false
		}
	}
	return true
}
