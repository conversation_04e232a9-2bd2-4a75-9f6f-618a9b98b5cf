package database

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"cmdb-scanner/internal/config"
	"cmdb-scanner/internal/types"

	_ "github.com/go-sql-driver/mysql"
	"github.com/sirupsen/logrus"
)

// MySQLClient MySQL客户端
type MySQLClient struct {
	cfg    *config.MySQLConfig
	logger *logrus.Logger
	db     *sql.DB
}

// NewMySQLClient 创建MySQL客户端
func NewMySQLClient(cfg *config.MySQLConfig) (*MySQLClient, error) {
	logger := logrus.New()
	logger.Infof("[NewMySQLClient] 🔌 创建MySQL客户端")
	logger.Infof("[NewMySQLClient] 🌐 连接地址: %s:%d", cfg.Host, cfg.Port)
	logger.Infof("[NewMySQLClient] 🗄️  数据库: %s", cfg.Database)
	logger.Infof("[NewMySQLClient] 👤 用户名: %s", cfg.Username)

	// 构建DSN - 添加超时参数
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local&timeout=60s&readTimeout=300s&writeTimeout=60s",
		cfg.Username, cfg.Password, cfg.Host, cfg.Port, cfg.Database)

	// 建立连接
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		logger.Errorf("[NewMySQLClient] ❌ 连接失败: %v", err)
		return nil, fmt.Errorf("连接MySQL失败: %w", err)
	}

	// 设置连接池参数 - 优化高并发性能
	maxConns := cfg.MaxConnections
	if maxConns < 50 {
		maxConns = 50 // 确保足够的连接数支持并发查询
	}
	db.SetMaxOpenConns(maxConns)
	db.SetMaxIdleConns(maxConns / 2) // 设置合理的空闲连接数
	db.SetConnMaxLifetime(time.Hour)
	// 设置连接超时
	db.SetConnMaxIdleTime(30 * time.Minute)

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		logger.Errorf("[NewMySQLClient] ❌ 连接测试失败: %v", err)
		return nil, fmt.Errorf("MySQL连接测试失败: %w", err)
	}

	client := &MySQLClient{
		cfg:    cfg,
		logger: logger,
		db:     db,
	}

	logger.Infof("[NewMySQLClient] ✅ MySQL客户端创建完成")
	return client, nil
}

// IsConnected 检查连接状态
func (c *MySQLClient) IsConnected() bool {
	if c.db == nil {
		return false
	}
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	return c.db.PingContext(ctx) == nil
}

// Close 关闭连接
func (c *MySQLClient) Close() error {
	if c.db != nil {
		return c.db.Close()
	}
	return nil
}

// BatchQueryMetaComponentData 批量查询Meta组件数据
func (c *MySQLClient) BatchQueryMetaComponentData(clusterIDs []string, workloadName string, date time.Time, fuzzySearch bool) (*types.MetaComponentDataBatch, error) {
	// MySQL数据更新有延迟，查询前一天的数据
	// queryDate := date.AddDate(0, 0, -1)
	queryDate := date

	c.logger.Infof("[BatchQueryMetaComponentData] 🔍 查询Meta组件数据")
	c.logger.Infof("[BatchQueryMetaComponentData] 集群数量: %d, workload: %s, 原始日期: %s, 查询日期: %s",
		len(clusterIDs), workloadName, date.Format("2006-01-02"), queryDate.Format("2006-01-02"))

	// 添加前几个集群ID的调试信息
	if len(clusterIDs) > 0 {
		c.logger.Infof("[BatchQueryMetaComponentData] 📋 前5个集群ID示例: %v", clusterIDs[:min(5, len(clusterIDs))])
	}

	if len(clusterIDs) == 0 {
		return &types.MetaComponentDataBatch{
			ClusterComponentData: make(map[string]*types.MetaClusterComponentInfo),
		}, nil
	}

	// 构建查询语句 - 根据fuzzySearch参数选择不同的查询条件
	var query string
	var workloadCondition string

	if fuzzySearch {
		// 模糊查询：使用多种模式查询name字段
		// 支持前缀、后缀、包含等多种模式
		workloadCondition = "(name LIKE ? OR name LIKE ? OR name LIKE ?)"
		c.logger.Infof("[BatchQueryMetaComponentData] 🔍 使用增强模糊查询模式，搜索name字段: %s", workloadName)
		c.logger.Infof("[BatchQueryMetaComponentData] 🔍 查询模式: 前缀匹配(%s-*), 后缀匹配(*-%s), 包含匹配(*%s*)", workloadName, workloadName, workloadName)
	} else {
		// 精确查询：使用ownerApp字段进行精确匹配
		workloadCondition = "ownerApp = ?"
		c.logger.Infof("[BatchQueryMetaComponentData] 🔍 使用精确查询模式，匹配ownerApp: %s", workloadName)
	}

	query = fmt.Sprintf(`
		SELECT 
			clusterId,
			region,
			product,
			COUNT(DISTINCT zone) AS zone_count,
			COUNT(*) AS pod_count,
			GROUP_CONCAT(DISTINCT zone ORDER BY zone SEPARATOR ',') AS zone_list
		FROM pods 
		WHERE clusterId IN (%%s)
			AND %s
			AND product IN ('tke', 'eks')
			AND status = 'Running'
			AND recordTime >= ? AND recordTime < ?
		GROUP BY clusterId, region, product
	`, workloadCondition)

	// 构建IN子句的占位符
	placeholders := make([]string, len(clusterIDs))
	for i := range placeholders {
		placeholders[i] = "?"
	}
	query = fmt.Sprintf(query, strings.Join(placeholders, ","))

	// 构建查询参数 - 根据fuzzySearch参数设置不同的参数值
	args := []interface{}{}
	for _, clusterID := range clusterIDs {
		args = append(args, clusterID)
	}

	// 根据查询模式设置workload参数
	if fuzzySearch {
		// 增强模糊查询：支持多种匹配模式
		// 1. 前缀匹配: kubejarvis-*
		args = append(args, workloadName+"-"+"%")
		// 2. 后缀匹配: *-kubejarvis
		args = append(args, "%"+"-"+workloadName)
		// 3. 包含匹配: *kubejarvis*
		args = append(args, "%"+workloadName+"%")
		c.logger.Infof("[BatchQueryMetaComponentData] 🔍 模糊查询参数: ['%s-%%', '%%-%s', '%%%s%%']", workloadName, workloadName, workloadName)
	} else {
		// 精确查询：直接使用workloadName
		args = append(args, workloadName)
	}

	// 查询日期的开始和结束时间
	startTime := queryDate.Format("2006-01-02 00:00:00")
	endTime := queryDate.AddDate(0, 0, 1).Format("2006-01-02 00:00:00")
	args = append(args, startTime)
	args = append(args, endTime)

	c.logger.Infof("[BatchQueryMetaComponentData] 📝 SQL: %s", query)
	c.logger.Infof("[BatchQueryMetaComponentData] 📋 参数数量: %d", len(args))
	queryMode := "精确查询(ownerApp)"
	if fuzzySearch {
		queryMode = "模糊查询(name)"
	}
	c.logger.Infof("[BatchQueryMetaComponentData] 📋 查询参数: workload=%s, mode=%s, date_range=%s to %s", workloadName, queryMode, startTime, endTime)
	
	// 执行主查询 - 使用合理的超时时间和重试机制
	var rows *sql.Rows
	var err error

	// 重试机制：最多重试3次
	maxRetries := 3
	for attempt := 1; attempt <= maxRetries; attempt++ {
		// 设置较短的超时时间：60秒
		ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)

		rows, err = c.db.QueryContext(ctx, query, args...)
		cancel()

		if err == nil {
			break // 查询成功，跳出重试循环
		}

		c.logger.Warnf("[BatchQueryMetaComponentData] ⚠️  查询失败 (尝试 %d/%d): %v", attempt, maxRetries, err)

		if attempt < maxRetries {
			// 等待一段时间后重试
			waitTime := time.Duration(attempt) * 2 * time.Second
			c.logger.Infof("[BatchQueryMetaComponentData] 🔄 等待 %v 后重试...", waitTime)
			time.Sleep(waitTime)
		}
	}

	if err != nil {
		c.logger.Errorf("[BatchQueryMetaComponentData] ❌ 查询失败 (已重试%d次): %v", maxRetries, err)
		return nil, fmt.Errorf("查询Meta组件数据失败: %w", err)
	}
	defer rows.Close()

	// 处理结果
	clusterComponentData := make(map[string]*types.MetaClusterComponentInfo)

	for rows.Next() {
		var clusterID, region, product, zoneListStr string
		var zoneCount, podCount int

		if err := rows.Scan(&clusterID, &region, &product, &zoneCount, &podCount, &zoneListStr); err != nil {
			c.logger.Errorf("[BatchQueryMetaComponentData] ❌ 扫描行失败: %v", err)
			continue
		}

		// 解析zone列表
		var zones []string
		if zoneListStr != "" {
			zones = strings.Split(zoneListStr, ",")
			// 去除空白字符
			for i, zone := range zones {
				zones[i] = strings.TrimSpace(zone)
			}
		}

		// 调试：检查product字段的值
		if product == "" || product == "null" {
			c.logger.Warnf("[BatchQueryMetaComponentData] ⚠️  集群 %s 的product字段为空或null: '%s'", clusterID, product)
		} else if product != "tke" && product != "eks" {
			c.logger.Warnf("[BatchQueryMetaComponentData] ⚠️  集群 %s 的product字段值异常: '%s' (不是tke或eks)", clusterID, product)
		}

		clusterComponentData[clusterID] = &types.MetaClusterComponentInfo{
			ClusterID:     clusterID,
			ComponentName: workloadName,
			Region:        region,
			ClusterType:   product, // product字段对应cluster_type
			ZoneCount:     zoneCount,
			PodCount:      podCount,
			Zones:         zones, // 添加具体的zone列表
			Status:        "running",
		}

		c.logger.Debugf("[BatchQueryMetaComponentData] 📊 集群 %s: region=%s, type='%s', zones=%d, pods=%d, zone_list=%v",
			clusterID, region, product, zoneCount, podCount, zones)
	}

	if err := rows.Err(); err != nil {
		c.logger.Errorf("[BatchQueryMetaComponentData] ❌ 行遍历错误: %v", err)
		return nil, fmt.Errorf("处理查询结果失败: %w", err)
	}

	c.logger.Infof("[BatchQueryMetaComponentData] ✅ 查询完成，获得%d个集群的数据", len(clusterComponentData))

	return &types.MetaComponentDataBatch{
		ClusterComponentData: clusterComponentData,
	}, nil
}

// BatchQueryMetaNodeInfo 批量查询Meta节点信息（预留方法）
func (c *MySQLClient) BatchQueryMetaNodeInfo(nodeNames []string, date time.Time) (map[string]types.MetaNodeInfo, error) {
	c.logger.Debugf("[BatchQueryMetaNodeInfo] 🔍 查询Meta节点信息（预留方法）")
	c.logger.Debugf("[BatchQueryMetaNodeInfo] 节点数量: %d, 日期: %s", len(nodeNames), date.Format("2006-01-02"))

	// 当前Meta集群检查不需要详细的节点信息，返回空结果
	return make(map[string]types.MetaNodeInfo), nil
}

// 辅助函数：取最小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// GetMetaClusterIDs 获取Meta集群的cluster_id列表
func (c *MySQLClient) GetMetaClusterIDs(componentNames []string, date time.Time) ([]string, error) {
	// MySQL数据更新有延迟，查询前一天的数据
	queryDate := date.AddDate(0, 0, -1)

	c.logger.Infof("[GetMetaClusterIDs] 🔍 获取Meta集群ID")
	c.logger.Infof("[GetMetaClusterIDs] 组件数量: %d, 原始日期: %s, 查询日期: %s", len(componentNames), date.Format("2006-01-02"), queryDate.Format("2006-01-02"))

	if len(componentNames) == 0 {
		return []string{}, nil
	}

	// 构建查询语句 - 获取有指定组件的所有集群ID，优化查询性能
	query := `
		SELECT DISTINCT clusterId
		FROM pods 
		WHERE ownerApp IN (%s)
			AND product IN ('tke', 'eks')
			AND status = 'Running'
			AND recordTime >= ? AND recordTime < ?
			AND clusterId != ''
	`

	// 构建IN子句的占位符
	placeholders := make([]string, len(componentNames))
	for i := range placeholders {
		placeholders[i] = "?"
	}
	query = fmt.Sprintf(query, strings.Join(placeholders, ","))

	// 构建查询参数 - 使用时间范围
	args := []interface{}{}
	for _, componentName := range componentNames {
		args = append(args, componentName)
	}
	// 查询日期的开始和结束时间
	startTime := queryDate.Format("2006-01-02 00:00:00")
	endTime := queryDate.AddDate(0, 0, 1).Format("2006-01-02 00:00:00")
	args = append(args, startTime)
	args = append(args, endTime)

	c.logger.Debugf("[GetMetaClusterIDs] 📝 SQL: %s", query)
	c.logger.Debugf("[GetMetaClusterIDs] 📋 参数数量: %d", len(args))

	// 执行查询 - 增加超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()

	rows, err := c.db.QueryContext(ctx, query, args...)
	if err != nil {
		c.logger.Errorf("[GetMetaClusterIDs] ❌ 查询失败: %v", err)
		return nil, fmt.Errorf("查询Meta集群ID失败: %w", err)
	}
	defer rows.Close()

	// 处理结果
	var clusterIDs []string
	for rows.Next() {
		var clusterID string
		if err := rows.Scan(&clusterID); err != nil {
			c.logger.Errorf("[GetMetaClusterIDs] ❌ 扫描行失败: %v", err)
			continue
		}
		clusterIDs = append(clusterIDs, clusterID)
	}

	if err := rows.Err(); err != nil {
		c.logger.Errorf("[GetMetaClusterIDs] ❌ 行遍历错误: %v", err)
		return nil, fmt.Errorf("处理查询结果失败: %w", err)
	}

	c.logger.Infof("[GetMetaClusterIDs] ✅ 查询完成，获得%d个Meta集群ID", len(clusterIDs))
	return clusterIDs, nil
}
