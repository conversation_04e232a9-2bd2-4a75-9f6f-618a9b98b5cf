package database

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"cmdb-scanner/internal/config"
	"cmdb-scanner/internal/output"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/sirupsen/logrus"
)

// ElasticsearchClient Elasticsearch客户端
type ElasticsearchClient struct {
	client      *elasticsearch.Client
	indexPrefix string
	logger      *logrus.Logger
}

// NewElasticsearchClient 创建Elasticsearch客户端
func NewElasticsearchClient(cfg *config.ElasticsearchConfig) (*ElasticsearchClient, error) {
	// 创建客户端配置
	esConfig := elasticsearch.Config{
		Addresses: cfg.Addresses,
		Username:  cfg.Username,
		Password:  cfg.Password,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: !cfg.VerifySSL,
			},
		},
		MaxRetries: cfg.MaxRetries,
	}

	client, err := elasticsearch.NewClient(esConfig)
	if err != nil {
		return nil, fmt.Errorf("创建elasticsearch客户端失败: %w", err)
	}

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	res, err := client.Ping(client.Ping.WithContext(ctx))
	if err != nil {
		return nil, fmt.Errorf("elasticsearch连接测试失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("elasticsearch连接测试失败: %s", res.String())
	}

	return &ElasticsearchClient{
		client:      client,
		indexPrefix: cfg.IndexPrefix,
		logger:      logrus.New(),
	}, nil
}

// IsConnected 检查连接状态
func (c *ElasticsearchClient) IsConnected() bool {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	res, err := c.client.Ping(c.client.Ping.WithContext(ctx))
	if err != nil {
		return false
	}
	defer res.Body.Close()

	return !res.IsError()
}

// IndexScanResults 索引扫描结果
func (c *ElasticsearchClient) IndexScanResults(scanResults []map[string]interface{}, scanDate time.Time) []string {
	if len(scanResults) == 0 {
		c.logger.Warning("没有扫描结果需要索引")
		return []string{}
	}

	// 在索引之前，删除当天的所有相关索引
	dateStr := scanDate.Format("2006-01-02")
	c.deleteExistingIndices(dateStr)

	// 使用OutputFormatter格式化数据为嵌套结构
	formatter := &output.OutputFormatter{}
	formattedClusters := formatter.FormatClusterOutput(scanResults)

	// 添加时间戳
	now := time.Now()
	scanDateStr := now.Format("2006-01-02")
	timestampStr := now.Format("2006-01-02T15:04:05.000000")

	// 处理新的数组结构
	clusterArray, ok := formattedClusters.([]map[string]interface{})
	if !ok {
		c.logger.Error("格式化的集群数据类型错误")
		return []string{}
	}

	// 为每个集群添加时间戳
	for _, cluster := range clusterArray {
		cluster["scan_date"] = scanDateStr
		cluster["timestamp"] = timestampStr
	}

	// 按集群类型分组
	clusterGroups := make(map[string][]map[string]interface{})
	for _, cluster := range clusterArray {
		clusterMetadata, ok := cluster["cluster_metadata"].(map[string]interface{})
		if !ok {
			continue
		}

		// 检查是否有真正的unknown状态（排除not_deployed状态）
		hasUnknownReason := false

		// 检查用户集群组件
		if userComponents, ok := cluster["user_cluster_components"].(map[string]interface{}); ok {
			for _, component := range userComponents {
				if componentMap, ok := component.(map[string]interface{}); ok {
					if inspections, ok := componentMap["inspections"].(map[string]interface{}); ok {
						for _, inspection := range inspections {
							if inspectionMap, ok := inspection.(map[string]interface{}); ok {
								// 检查状态是否为unknown（排除not_deployed）
								if status, exists := inspectionMap["status"]; exists && status == "unknown" {
									hasUnknownReason = true
									break
								}
							}
						}
						if hasUnknownReason {
							break
						}
					}
				}
				if hasUnknownReason {
					break
				}
			}
		}

		// 检查Meta集群组件
		if !hasUnknownReason {
			if metaComponents, ok := cluster["meta_cluster_components"].(map[string]interface{}); ok {
				for _, component := range metaComponents {
					if componentMap, ok := component.(map[string]interface{}); ok {
						if inspections, ok := componentMap["inspections"].(map[string]interface{}); ok {
							for _, inspection := range inspections {
								if inspectionMap, ok := inspection.(map[string]interface{}); ok {
									// 检查状态是否为unknown（排除not_deployed）
									if status, exists := inspectionMap["status"]; exists && status == "unknown" {
										hasUnknownReason = true
										break
									}
								}
							}
							if hasUnknownReason {
								break
							}
						}
					}
					if hasUnknownReason {
						break
					}
				}
			}
		}

		var clusterType string
		if hasUnknownReason {
			// 如果有unknown_reason，归类为unknown
			clusterType = "unknown"
		} else {
			// 否则根据cluster_type归类
			clusterTypeRaw, ok := clusterMetadata["cluster_type"].(string)
			if !ok || clusterTypeRaw == "" {
				clusterType = "unknown"
			} else {
				// 标准化集群类型名称，转换为小写
				clusterType = strings.ToLower(clusterTypeRaw)

				// 处理特殊情况
				switch clusterType {
				case "tke":
					clusterType = "tke"
				case "eks":
					clusterType = "eks"
				default:
					clusterType = "unknown"
				}
			}
		}

		clusterGroups[clusterType] = append(clusterGroups[clusterType], cluster)
	}

	var indexedIndices []string
	dateStr = scanDate.Format("2006-01-02") // 格式：2025-08-01

	for clusterType, results := range clusterGroups {
		// 新的索引名格式：starship-{clustertype}-scanner-{date}
		clusterTypeLower := strings.ToLower(clusterType)
		indexName := fmt.Sprintf("starship-%s-scanner-%s", clusterTypeLower, dateStr)

		if c.bulkIndexDocuments(indexName, results) {
			indexedIndices = append(indexedIndices, indexName)
			c.logger.Infof("成功索引 %d 个结果到 %s", len(results), indexName)
		} else {
			c.logger.Errorf("索引到 %s 失败", indexName)
		}
	}

	return indexedIndices
}

// bulkIndexDocuments 批量索引文档
func (c *ElasticsearchClient) bulkIndexDocuments(indexName string, documents []map[string]interface{}) bool {
	if len(documents) == 0 {
		return true
	}

	// 创建索引（如果不存在）
	if !c.createIndexIfNotExists(indexName) {
		return false
	}

	// 分批索引，使用较大的批次大小提高性能
	batchSize := 10000
	successCount := 0

	for i := 0; i < len(documents); i += batchSize {
		end := i + batchSize
		if end > len(documents) {
			end = len(documents)
		}
		batch := documents[i:end]

		// 准备批量索引数据
		var buf bytes.Buffer
		for _, doc := range batch {
			// 获取cluster_id作为文档ID
			clusterID, _ := doc["cluster_id"].(string)
			if clusterID == "" {
				c.logger.Warnf("文档缺少cluster_id，跳过索引")
				continue
			}

			// 添加索引操作，使用cluster_id作为文档ID
			indexAction := map[string]interface{}{
				"index": map[string]interface{}{
					"_index": indexName,
					"_id":    clusterID,
				},
			}
			actionBytes, _ := json.Marshal(indexAction)
			buf.Write(actionBytes)
			buf.WriteByte('\n')

			// 添加文档数据（这就是_source的内容）
			docBytes, _ := json.Marshal(doc)
			buf.Write(docBytes)
			buf.WriteByte('\n')
		}

		// 执行批量索引
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)

		response, err := c.client.Bulk(bytes.NewReader(buf.Bytes()), c.client.Bulk.WithContext(ctx))
		cancel()

		if err != nil {
			c.logger.Errorf("批量索引失败: %v", err)
			continue
		}
		defer response.Body.Close()

		if response.IsError() {
			c.logger.Errorf("批量索引响应错误: %s", response.String())
			// 如果是413错误，尝试更小的批次
			if strings.Contains(response.String(), "413") || strings.Contains(response.String(), "Request Entity Too Large") {
				c.logger.Warnf("数据量过大，尝试更小的批次大小")
				// 将当前批次分成更小的子批次
				smallBatchSize := 50
				for j := 0; j < len(batch); j += smallBatchSize {
					end := j + smallBatchSize
					if end > len(batch) {
						end = len(batch)
					}
					smallBatch := batch[j:end]

					// 重新构建小批次的请求体
					var smallBuf bytes.Buffer
					for _, doc := range smallBatch {
						indexAction := map[string]interface{}{
							"index": map[string]interface{}{
								"_index": indexName,
							},
						}
						if err := json.NewEncoder(&smallBuf).Encode(indexAction); err != nil {
							continue
						}
						if err := json.NewEncoder(&smallBuf).Encode(doc); err != nil {
							continue
						}
					}

					// 发送小批次请求
					smallResponse, err := c.client.Bulk(bytes.NewReader(smallBuf.Bytes()))
					if err == nil && !smallResponse.IsError() {
						successCount += len(smallBatch)
						// 减少小批次的日志输出
						if len(smallBatch) >= 50 {
							c.logger.Infof("成功索引小批次 %d 个文档", len(smallBatch))
						}
					} else {
						c.logger.Errorf("小批次索引也失败: %v", err)
					}
				}
			}
			continue
		}

		successCount += len(batch)
		// 只在每10个批次或最后一个批次时打印日志，减少日志量
		if (i/batchSize+1)%10 == 0 || i+batchSize >= len(documents) {
			c.logger.Infof("成功索引批次 %d 个文档 (进度: %d/%d)", len(batch), successCount, len(documents))
		}
	}

	c.logger.Infof("总共成功索引 %d/%d 个文档到 %s", successCount, len(documents), indexName)

	// 刷新索引以确保数据立即可查询
	if successCount > 0 {
		if c.refreshIndex(indexName) {
			c.logger.Infof("✅ 索引 %s 刷新成功，数据现在可以查询", indexName)

			// 验证数据是否真正写入成功
			if c.verifyIndexDocuments(indexName, len(documents)) {
				c.logger.Infof("✅ 数据验证成功: 索引 %s 中有 %d 个文档可查询", indexName, len(documents))
			} else {
				c.logger.Errorf("❌ 数据验证失败: 索引 %s 中的文档数量与预期不符", indexName)
			}
		} else {
			c.logger.Warnf("⚠️  索引 %s 刷新失败，数据可能需要等待1秒后才能查询", indexName)
		}
	}

	return successCount > 0
}

// createIndexIfNotExists 创建索引（如果不存在）
func (c *ElasticsearchClient) createIndexIfNotExists(indexName string) bool {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 检查索引是否存在
	res, err := c.client.Indices.Exists([]string{indexName}, c.client.Indices.Exists.WithContext(ctx))
	if err != nil {
		c.logger.Errorf("检查索引存在性失败: %v", err)
		return false
	}
	defer res.Body.Close()

	if res.StatusCode == 200 {
		return true // 索引已存在
	}

	// 创建索引
	mapping := map[string]interface{}{
		"settings": map[string]interface{}{
			"refresh_interval": "1s", // 确保数据能够及时刷新
			"number_of_shards":   1,
			"number_of_replicas": 1,
		},
		"mappings": map[string]interface{}{
			"properties": map[string]interface{}{
				"cluster_id": map[string]interface{}{
					"type": "keyword",
				},
				"cluster_name": map[string]interface{}{
					"type": "keyword",
				},
				"cluster_type": map[string]interface{}{
					"type": "keyword",
				},
				"region": map[string]interface{}{
					"type": "keyword",
				},
				"status": map[string]interface{}{
					"type": "keyword",
				},
				"inspector_id": map[string]interface{}{
					"type": "keyword",
				},
				"workload_name": map[string]interface{}{
					"type": "keyword",
				},
				"component_name": map[string]interface{}{
					"type": "keyword",
				},
				"scan_date": map[string]interface{}{
					"type": "date",
				},
				"timestamp": map[string]interface{}{
					"type": "date",
					"format": "yyyy-MM-dd'T'HH:mm:ss.SSSSSS",
				},
			},
		},
	}

	mappingBytes, _ := json.Marshal(mapping)
	res, err = c.client.Indices.Create(
		indexName,
		c.client.Indices.Create.WithContext(ctx),
		c.client.Indices.Create.WithBody(bytes.NewReader(mappingBytes)),
	)
	if err != nil {
		c.logger.Errorf("创建索引失败: %v", err)
		return false
	}
	defer res.Body.Close()

	if res.IsError() {
		c.logger.Errorf("创建索引响应错误: %s", res.String())
		return false
	}

	return true
}

// deleteExistingIndices 删除指定日期的所有扫描索引
func (c *ElasticsearchClient) deleteExistingIndices(dateStr string) {
	// 定义需要删除的索引模式
	indexPatterns := []string{
		fmt.Sprintf("starship-tke-scanner-%s", dateStr),
		fmt.Sprintf("starship-eks-scanner-%s", dateStr),
		fmt.Sprintf("starship-unknown-scanner-%s", dateStr),
	}

	c.logger.Infof("🗑️  开始删除日期 %s 的现有索引", dateStr)

	for _, indexName := range indexPatterns {
		if c.deleteIndexIfExists(indexName) {
			c.logger.Infof("✅ 成功删除索引: %s", indexName)
		} else {
			c.logger.Debugf("📋 索引不存在或删除失败: %s", indexName)
		}
	}

	c.logger.Infof("🧹 索引清理完成")
}

// deleteIndexIfExists 删除索引（如果存在）
func (c *ElasticsearchClient) deleteIndexIfExists(indexName string) bool {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 检查索引是否存在
	res, err := c.client.Indices.Exists([]string{indexName}, c.client.Indices.Exists.WithContext(ctx))
	if err != nil {
		c.logger.Errorf("检查索引存在性失败: %v", err)
		return false
	}
	defer res.Body.Close()

	if res.StatusCode != 200 {
		// 索引不存在
		return false
	}

	// 删除索引
	deleteRes, err := c.client.Indices.Delete([]string{indexName}, c.client.Indices.Delete.WithContext(ctx))
	if err != nil {
		c.logger.Errorf("删除索引失败: %v", err)
		return false
	}
	defer deleteRes.Body.Close()

	if deleteRes.IsError() {
		c.logger.Errorf("删除索引响应错误: %s", deleteRes.String())
		return false
	}

	return true
}

// refreshIndex 刷新索引以确保数据立即可查询
func (c *ElasticsearchClient) refreshIndex(indexName string) bool {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	res, err := c.client.Indices.Refresh(
		c.client.Indices.Refresh.WithIndex(indexName),
		c.client.Indices.Refresh.WithContext(ctx),
	)
	if err != nil {
		c.logger.Errorf("刷新索引失败: %v", err)
		return false
	}
	defer res.Body.Close()

	if res.IsError() {
		c.logger.Errorf("刷新索引响应错误: %s", res.String())
		return false
	}

	return true
}

// verifyIndexDocuments 验证索引中的文档数量
func (c *ElasticsearchClient) verifyIndexDocuments(indexName string, expectedCount int) bool {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 使用 _count API 获取文档数量
	res, err := c.client.Count(
		c.client.Count.WithIndex(indexName),
		c.client.Count.WithContext(ctx),
	)
	if err != nil {
		c.logger.Errorf("验证索引文档数量失败: %v", err)
		return false
	}
	defer res.Body.Close()

	if res.IsError() {
		c.logger.Errorf("验证索引文档数量响应错误: %s", res.String())
		return false
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		c.logger.Errorf("解析文档数量响应失败: %v", err)
		return false
	}

	actualCount, ok := result["count"].(float64)
	if !ok {
		c.logger.Errorf("无法获取文档数量")
		return false
	}

	c.logger.Infof("📊 索引 %s 验证结果: 期望 %d 个文档, 实际 %.0f 个文档",
		indexName, expectedCount, actualCount)

	return int(actualCount) == expectedCount
}

// GetElasticsearchClient 获取Elasticsearch客户端（单例模式）
func GetElasticsearchClient(cfg *config.ElasticsearchConfig) *ElasticsearchClient {
	client, err := NewElasticsearchClient(cfg)
	if err != nil {
		logrus.WithError(err).Error("创建Elasticsearch客户端失败")
		return nil
	}
	return client
}
