package database

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"sync"
	"time"

	"cmdb-scanner/internal/types"

	"github.com/sirupsen/logrus"
)

// UnifiedMetaDataQuery 统一的Meta数据查询器
type UnifiedMetaDataQuery struct {
	client *MySQLClient
	logger *logrus.Logger
	
	// 查询缓存
	queryCache map[string]*types.UnifiedMetaDataResult
	cacheMutex sync.RWMutex
}

// NewUnifiedMetaDataQuery 创建统一的Meta数据查询器
func NewUnifiedMetaDataQuery(client *MySQLClient, logger *logrus.Logger) *UnifiedMetaDataQuery {
	return &UnifiedMetaDataQuery{
		client:     client,
		logger:     logger,
		queryCache: make(map[string]*types.UnifiedMetaDataResult),
	}
}

// QueryAllMetaComponentData 一次性查询所有Meta组件数据
func (q *UnifiedMetaDataQuery) QueryAllMetaComponentData(
	clusterIDs []string,
	workloadConfigs map[string]bool, // workloadName -> fuzzySearch
	date time.Time,
) (*types.UnifiedMetaDataResult, error) {
	
	// 生成缓存键
	cacheKey := q.generateCacheKey(clusterIDs, workloadConfigs, date)
	
	// 检查缓存
	q.cacheMutex.RLock()
	if cached, exists := q.queryCache[cacheKey]; exists {
		q.cacheMutex.RUnlock()
		q.logger.Infof("🎯 [UnifiedMetaDataQuery] 使用缓存数据，集群数量: %d", len(clusterIDs))
		return cached, nil
	}
	q.cacheMutex.RUnlock()

	q.logger.Infof("🚀 [UnifiedMetaDataQuery] 开始统一查询Meta组件数据")
	q.logger.Infof("📊 [UnifiedMetaDataQuery] 集群数量: %d, workload数量: %d", len(clusterIDs), len(workloadConfigs))
	
	startTime := time.Now()
	
	if len(clusterIDs) == 0 {
		return &types.UnifiedMetaDataResult{
			WorkloadData: make(map[string]*types.MetaComponentDataBatch),
		}, nil
	}

	// 构建统一查询语句
	query, args := q.buildUnifiedQuery(clusterIDs, workloadConfigs, date)
	
	// 执行查询
	result, err := q.executeUnifiedQuery(query, args, workloadConfigs)
	if err != nil {
		return nil, err
	}
	
	// 缓存结果
	q.cacheMutex.Lock()
	q.queryCache[cacheKey] = result
	q.cacheMutex.Unlock()
	
	duration := time.Since(startTime)
	q.logger.Infof("✅ [UnifiedMetaDataQuery] 统一查询完成，耗时: %v", duration)
	
	return result, nil
}

// buildUnifiedQuery 构建统一查询语句
func (q *UnifiedMetaDataQuery) buildUnifiedQuery(
	clusterIDs []string,
	workloadConfigs map[string]bool,
	date time.Time,
) (string, []interface{}) {
	
	// 构建workload条件
	var workloadConditions []string
	var args []interface{}
	
	// 添加集群ID参数
	placeholders := make([]string, len(clusterIDs))
	for i, clusterID := range clusterIDs {
		placeholders[i] = "?"
		args = append(args, clusterID)
	}
	
	// 为每个workload构建条件
	for workloadName, fuzzySearch := range workloadConfigs {
		if fuzzySearch {
			// 模糊查询条件
			condition := "(name LIKE ? OR name LIKE ? OR name LIKE ?)"
			workloadConditions = append(workloadConditions, fmt.Sprintf("(%s AND '%s' AS workload_name)", condition, workloadName))
			args = append(args, workloadName+"-"+"%", "%"+"-"+workloadName, "%"+workloadName+"%")
		} else {
			// 精确查询条件
			condition := "ownerApp = ?"
			workloadConditions = append(workloadConditions, fmt.Sprintf("(%s AND '%s' AS workload_name)", condition, workloadName))
			args = append(args, workloadName)
		}
	}
	
	// 构建完整查询
	query := fmt.Sprintf(`
		SELECT 
			workload_name,
			clusterId,
			region,
			product,
			COUNT(DISTINCT zone) AS zone_count,
			COUNT(*) AS pod_count,
			GROUP_CONCAT(DISTINCT zone ORDER BY zone SEPARATOR ',') AS zone_list
		FROM (
			%s
		) AS unified_query
		WHERE clusterId IN (%s)
			AND product IN ('tke', 'eks')
			AND status = 'Running'
			AND recordTime >= ? AND recordTime < ?
		GROUP BY workload_name, clusterId, region, product
	`, 
		q.buildUnionQuery(workloadConfigs),
		strings.Join(placeholders, ","))
	
	// 添加时间参数
	startTime := date.Format("2006-01-02 00:00:00")
	endTime := date.AddDate(0, 0, 1).Format("2006-01-02 00:00:00")
	args = append(args, startTime, endTime)
	
	return query, args
}

// buildUnionQuery 构建UNION查询
func (q *UnifiedMetaDataQuery) buildUnionQuery(workloadConfigs map[string]bool) string {
	var unionParts []string
	
	for workloadName, fuzzySearch := range workloadConfigs {
		var condition string
		if fuzzySearch {
			condition = "(name LIKE ? OR name LIKE ? OR name LIKE ?)"
		} else {
			condition = "ownerApp = ?"
		}
		
		unionPart := fmt.Sprintf(`
			SELECT 
				clusterId, region, product, zone, status, recordTime,
				'%s' AS workload_name
			FROM pods 
			WHERE %s
		`, workloadName, condition)
		
		unionParts = append(unionParts, unionPart)
	}
	
	return strings.Join(unionParts, " UNION ALL ")
}

// executeUnifiedQuery 执行统一查询
func (q *UnifiedMetaDataQuery) executeUnifiedQuery(
	query string,
	args []interface{},
	workloadConfigs map[string]bool,
) (*types.UnifiedMetaDataResult, error) {
	
	q.logger.Infof("📝 [UnifiedMetaDataQuery] 执行统一SQL查询")
	q.logger.Debugf("📝 [UnifiedMetaDataQuery] SQL: %s", query)
	q.logger.Debugf("📋 [UnifiedMetaDataQuery] 参数数量: %d", len(args))
	
	// 执行查询 - 使用更长的超时时间，因为这是统一查询
	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()
	
	rows, err := q.client.db.QueryContext(ctx, query, args...)
	if err != nil {
		q.logger.Errorf("❌ [UnifiedMetaDataQuery] 统一查询失败: %v", err)
		return nil, fmt.Errorf("统一查询Meta组件数据失败: %w", err)
	}
	defer rows.Close()
	
	// 解析结果
	result := &types.UnifiedMetaDataResult{
		WorkloadData: make(map[string]*types.MetaComponentDataBatch),
	}
	
	// 为每个workload初始化数据结构
	for workloadName := range workloadConfigs {
		result.WorkloadData[workloadName] = &types.MetaComponentDataBatch{
			ClusterComponentData: make(map[string]*types.MetaClusterComponentInfo),
		}
	}
	
	// 处理查询结果
	for rows.Next() {
		var workloadName, clusterID, region, product, zoneList string
		var zoneCount, podCount int
		
		err := rows.Scan(&workloadName, &clusterID, &region, &product, &zoneCount, &podCount, &zoneList)
		if err != nil {
			q.logger.Errorf("❌ [UnifiedMetaDataQuery] 扫描结果失败: %v", err)
			continue
		}
		
		// 解析zone列表
		var zones []string
		if zoneList != "" {
			zones = strings.Split(zoneList, ",")
		}
		
		// 存储到对应workload的结果中
		if batch, exists := result.WorkloadData[workloadName]; exists {
			batch.ClusterComponentData[clusterID] = &types.MetaClusterComponentInfo{
				ClusterID:    clusterID,
				Region:       region,
				Product:      product,
				ZoneCount:    zoneCount,
				PodCount:     podCount,
				ZoneList:     zones,
			}
		}
	}
	
	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历查询结果失败: %w", err)
	}
	
	// 统计结果
	totalClusters := 0
	for workloadName, batch := range result.WorkloadData {
		clusterCount := len(batch.ClusterComponentData)
		totalClusters += clusterCount
		q.logger.Infof("📊 [UnifiedMetaDataQuery] workload %s: %d 个集群", workloadName, clusterCount)
	}
	
	q.logger.Infof("✅ [UnifiedMetaDataQuery] 统一查询结果: %d 个workload, 总计 %d 个集群数据", len(result.WorkloadData), totalClusters)
	
	return result, nil
}

// generateCacheKey 生成缓存键
func (q *UnifiedMetaDataQuery) generateCacheKey(
	clusterIDs []string,
	workloadConfigs map[string]bool,
	date time.Time,
) string {
	// 简化的缓存键生成逻辑
	return fmt.Sprintf("meta_query_%d_clusters_%d_workloads_%s", 
		len(clusterIDs), len(workloadConfigs), date.Format("2006-01-02"))
}

// ClearCache 清理缓存
func (q *UnifiedMetaDataQuery) ClearCache() {
	q.cacheMutex.Lock()
	defer q.cacheMutex.Unlock()
	q.queryCache = make(map[string]*types.UnifiedMetaDataResult)
	q.logger.Infof("🧹 [UnifiedMetaDataQuery] 缓存已清理")
}
