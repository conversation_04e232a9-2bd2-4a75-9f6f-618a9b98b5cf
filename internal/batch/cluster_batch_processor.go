package batch

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"cmdb-scanner/internal/cache"
	"cmdb-scanner/internal/config"
	"cmdb-scanner/internal/database"
	"cmdb-scanner/internal/parallel"
	"cmdb-scanner/internal/types"

	"github.com/sirupsen/logrus"
)

// ClusterBatchProcessor 集群批量处理器
type ClusterBatchProcessor struct {
	clusterIDs         []string
	batchID            int
	clickhouseClient   *database.ClickHouseClient
	mysqlClient        *database.MySQLClient
	config             *config.Config
	scanDate           time.Time
	logger             *logrus.Logger
	cacheManager       *cache.DataCacheManager
	componentProcessor *parallel.ComponentProcessor

	// 性能统计
	performanceStats map[string]time.Duration
}

// NewClusterBatchProcessor 创建集群批量处理器
func NewClusterBatchProcessor(
	clusterIDs []string,
	batchID int,
	clickhouseClient *database.ClickHouseClient,
	mysqlClient *database.MySQLClient,
	config *config.Config,
	scanDate time.Time,
	logger *logrus.Logger,
) *ClusterBatchProcessor {
	// 创建缓存管理器
	cacheManager := cache.NewDataCacheManager(
		mysqlClient, clickhouseClient, config, scanDate, logger,
	)

	// 创建并行组件处理器
	componentProcessor := parallel.NewComponentProcessor(
		clusterIDs, batchID, clickhouseClient, mysqlClient, config, scanDate, logger, cacheManager,
	)

	return &ClusterBatchProcessor{
		clusterIDs:         clusterIDs,
		batchID:            batchID,
		clickhouseClient:   clickhouseClient,
		mysqlClient:        mysqlClient,
		config:             config,
		scanDate:           scanDate,
		logger:             logger,
		cacheManager:       cacheManager,
		componentProcessor: componentProcessor,
		performanceStats:   make(map[string]time.Duration),
	}
}

// Process 处理批量集群检查（优化版本）
func (p *ClusterBatchProcessor) Process() (*types.BatchResult, error) {
	p.logger.Infof("🚀 [Process] 批次 %d 开始优化处理 %d 个集群", p.batchID, len(p.clusterIDs))
	overallStartTime := time.Now()

	// 1. 批量获取集群基础信息
	startTime := time.Now()
	clusterMetadata, err := p.getClusterMetadataBatch()
	if err != nil {
		return nil, fmt.Errorf("获取集群基础信息失败: %w", err)
	}
	p.performanceStats["cluster_metadata"] = time.Since(startTime)
	p.logger.Infof("✅ [Process] 集群基础信息获取完成，耗时: %v", p.performanceStats["cluster_metadata"])

	// 1.5. 识别独立集群（ClusterType为4的集群）- 修正命名
	startTime = time.Now()
	independentClusters, err := p.identifyIndependentClusters(clusterMetadata)
	if err != nil {
		p.logger.Errorf("[Process] 识别独立集群失败: %v", err)
		// 不中断流程，继续处理
		independentClusters = []string{}
	}
	p.performanceStats["identify_clusters"] = time.Since(startTime)
	p.logger.Infof("📋 [Process] 识别到 %d 个独立集群（ClusterType=4），耗时: %v", len(independentClusters), p.performanceStats["identify_clusters"])

	// 2. 预加载所有组件数据
	startTime = time.Now()
	err = p.cacheManager.PreloadAllComponentData(p.clusterIDs)
	if err != nil {
		return nil, fmt.Errorf("预加载组件数据失败: %w", err)
	}
	p.performanceStats["preload_data"] = time.Since(startTime)
	p.logger.Infof("✅ [Process] 数据预加载完成，耗时: %v", p.performanceStats["preload_data"])

	// 3. 并行处理用户集群组件
	startTime = time.Now()
	userResults, err := p.componentProcessor.ProcessUserComponentsParallel()
	if err != nil {
		return nil, fmt.Errorf("并行处理用户集群组件失败: %w", err)
	}
	p.performanceStats["user_components"] = time.Since(startTime)
	p.logger.Infof("✅ [Process] 用户组件并行处理完成，耗时: %v", p.performanceStats["user_components"])

	// 4. 并行处理Meta集群组件
	startTime = time.Now()
	metaResults, err := p.componentProcessor.ProcessMetaComponentsParallel()
	if err != nil {
		return nil, fmt.Errorf("并行处理Meta集群组件失败: %w", err)
	}
	p.performanceStats["meta_components"] = time.Since(startTime)
	p.logger.Infof("✅ [Process] Meta组件并行处理完成，耗时: %v", p.performanceStats["meta_components"])

	// 5. 组织结果数据
	startTime = time.Now()
	batchResult, err := p.organizeResults(clusterMetadata, userResults, metaResults)
	if err != nil {
		return nil, fmt.Errorf("组织结果数据失败: %w", err)
	}
	p.performanceStats["organize_results"] = time.Since(startTime)

	// 6. 打印性能统计
	totalTime := time.Since(overallStartTime)
	p.performanceStats["total"] = totalTime
	p.printPerformanceStats()

	p.logger.Infof("🎉 [Process] 批次 %d 优化处理完成，生成 %d 个集群结果，总耗时: %v", p.batchID, len(batchResult.Results), totalTime)
	return batchResult, nil
}

// getClusterMetadataBatch 批量获取集群基础信息
func (p *ClusterBatchProcessor) getClusterMetadataBatch() (map[string]types.ClusterMetadata, error) {
	p.logger.Debugf("[getClusterMetadataBatch] 批量查询 %d 个集群的基础信息", len(p.clusterIDs))

	clusterMetadata, err := p.clickhouseClient.GetClusterMetadataBatch(p.clusterIDs, p.scanDate)
	if err != nil {
		return nil, err
	}

	p.logger.Debugf("[getClusterMetadataBatch] 成功获取 %d 个集群的基础信息", len(clusterMetadata))
	return clusterMetadata, nil
}

// isInspectorDisabled 检查检查器是否被禁用
func (p *ClusterBatchProcessor) isInspectorDisabled(disabledInspectors []string, inspectorID string) bool {
	for _, disabled := range disabledInspectors {
		if disabled == inspectorID {
			return true
		}
	}
	return false
}

// organizeResults 组织结果数据
func (p *ClusterBatchProcessor) organizeResults(
	clusterMetadata map[string]types.ClusterMetadata,
	userResults map[string]map[string]map[string]types.InspectionResult,
	metaResults map[string]map[string]map[string]types.InspectionResult,
) (*types.BatchResult, error) {
	p.logger.Debugf("[organizeResults] 📋 组织 %d 个集群的结果数据", len(p.clusterIDs))

	batchResult := &types.BatchResult{
		BatchID:   p.batchID,
		Results:   make(map[string]*types.ClusterResult),
		Timestamp: time.Now(),
	}

	// 为每个集群创建结果
	for _, clusterID := range p.clusterIDs {
		clusterResult := &types.ClusterResult{
			ClusterID:        clusterID,
			ComponentResults: make(map[string]*types.ComponentResult),
		}

		// 添加集群基础信息
		if metadata, exists := clusterMetadata[clusterID]; exists {
			clusterResult.ClusterMetadata = metadata
		} else {
			// 如果没有找到基础信息，创建默认的
			clusterResult.ClusterMetadata = types.ClusterMetadata{
				ClusterID:  clusterID,
				DataSource: "unknown",
			}
		}

		// 处理用户集群组件结果
		if userClusterResults, exists := userResults[clusterID]; exists {
			for componentName, inspectorResults := range userClusterResults {
				if clusterResult.ComponentResults[componentName] == nil {
					clusterResult.ComponentResults[componentName] = &types.ComponentResult{
						ComponentName:     componentName,
						ComponentType:     "user_cluster",
						InspectionResults: make(map[string]types.InspectionResult),
					}
				}

				for inspectorID, result := range inspectorResults {
					clusterResult.ComponentResults[componentName].InspectionResults[inspectorID] = result
				}
			}
		}

		// 处理Meta集群组件结果
		if metaClusterResults, exists := metaResults[clusterID]; exists {
			for componentName, inspectorResults := range metaClusterResults {
				if clusterResult.ComponentResults[componentName] == nil {
					clusterResult.ComponentResults[componentName] = &types.ComponentResult{
						ComponentName:     componentName,
						ComponentType:     "meta_cluster",
						InspectionResults: make(map[string]types.InspectionResult),
					}
				}

				for inspectorID, result := range inspectorResults {
					clusterResult.ComponentResults[componentName].InspectionResults[inspectorID] = result
				}
			}
		}

		batchResult.Results[clusterID] = clusterResult
	}

	p.logger.Debugf("[organizeResults] ✅ 结果数据组织完成，生成 %d 个集群结果", len(batchResult.Results))
	return batchResult, nil
}

// identifyIndependentClusters 识别独立集群（ClusterType为4的集群）
func (p *ClusterBatchProcessor) identifyIndependentClusters(clusterMetadata map[string]types.ClusterMetadata) ([]string, error) {
	p.logger.Debugf("[identifyIndependentClusters] 开始识别独立集群")

	var independentClusters []string
	currentDate := p.scanDate.Format("2006-01-02")

	// 遍历集群基础信息，查找ClusterType为'tke'的集群
	for clusterID, metadata := range clusterMetadata {
		if strings.ToLower(metadata.ClusterType) == "tke" {
			p.logger.Debugf("[identifyIndependentClusters] 发现TKE集群: %s，开始查询详细信息", clusterID)

			// 对TKE集群执行ClickHouse查询
			query := "SELECT * FROM cluster WHERE DateDay = ? AND ClusterId = ?"

			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			results, err := p.clickhouseClient.ExecuteQuery(ctx, query, currentDate, clusterID)
			cancel()

			if err != nil {
				p.logger.Errorf("[identifySpecialTKEClusters] 查询集群 %s 详细信息失败: %v", clusterID, err)
				continue // 继续处理其他集群
			}

			// 解析查询结果
			for _, row := range results {
				dataField, exists := row["Data"]
				if !exists {
					p.logger.Warnf("[identifySpecialTKEClusters] 集群 %s 缺少Data字段", clusterID)
					continue
				}

				// 将Data字段转换为字符串
				dataStr, ok := dataField.(string)
				if !ok {
					p.logger.Warnf("[identifySpecialTKEClusters] 集群 %s 的Data字段不是字符串类型: %T", clusterID, dataField)
					continue
				}

				// 解析JSON数据
				var dataJSON map[string]interface{}
				if err := json.Unmarshal([]byte(dataStr), &dataJSON); err != nil {
					p.logger.Errorf("[identifySpecialTKEClusters] 解析集群 %s 的Data JSON失败: %v", clusterID, err)
					continue
				}

				// 检查Data中的ClusterType字段
				if clusterTypeField, exists := dataJSON["ClusterType"]; exists {
					// 尝试将ClusterType转换为数字
					var clusterTypeNum float64
					switch v := clusterTypeField.(type) {
					case float64:
						clusterTypeNum = v
					case int:
						clusterTypeNum = float64(v)
					case string:
						if parsed, err := strconv.ParseFloat(v, 64); err == nil {
							clusterTypeNum = parsed
						} else {
							p.logger.Warnf("[identifySpecialTKEClusters] 集群 %s 的ClusterType字段无法解析为数字: %s", clusterID, v)
							continue
						}
					default:
						p.logger.Warnf("[identifySpecialTKEClusters] 集群 %s 的ClusterType字段类型未知: %T", clusterID, v)
						continue
					}

					// 检查ClusterType是否等于4
					if clusterTypeNum == 4 {
						independentClusters = append(independentClusters, clusterID)
						p.logger.Infof("[identifyIndependentClusters] ✅ 发现独立集群: %s (ClusterType=4)", clusterID)
					} else {
						p.logger.Debugf("[identifySpecialTKEClusters] 集群 %s 的ClusterType为 %v，不是目标类型", clusterID, clusterTypeNum)
					}
				} else {
					p.logger.Warnf("[identifySpecialTKEClusters] 集群 %s 的Data中缺少ClusterType字段", clusterID)
				}
			}
		}
	}

	p.logger.Infof("[identifyIndependentClusters] ✅ 识别完成，共发现 %d 个独立集群", len(independentClusters))
	return independentClusters, nil
}

// printPerformanceStats 打印性能统计信息
func (p *ClusterBatchProcessor) printPerformanceStats() {
	p.logger.Infof("📊 [Performance] 批次 %d 性能统计:", p.batchID)
	p.logger.Infof("  集群基础信息获取: %v", p.performanceStats["cluster_metadata"])
	p.logger.Infof("  集群类型识别: %v", p.performanceStats["identify_clusters"])
	p.logger.Infof("  数据预加载: %v", p.performanceStats["preload_data"])
	p.logger.Infof("  用户组件处理: %v", p.performanceStats["user_components"])
	p.logger.Infof("  Meta组件处理: %v", p.performanceStats["meta_components"])
	p.logger.Infof("  结果组织: %v", p.performanceStats["organize_results"])
	p.logger.Infof("  总耗时: %v", p.performanceStats["total"])

	// 计算性能提升比例（假设原来的串行处理时间）
	totalTime := p.performanceStats["total"]
	estimatedSerialTime := p.performanceStats["user_components"] + p.performanceStats["meta_components"] +
		p.performanceStats["preload_data"]*2 // 假设串行处理需要重复查询

	if estimatedSerialTime > 0 {
		improvement := float64(estimatedSerialTime-totalTime) / float64(estimatedSerialTime) * 100
		p.logger.Infof("  🚀 预估性能提升: %.1f%%", improvement)
	}
}

// GetPerformanceStats 获取性能统计信息
func (p *ClusterBatchProcessor) GetPerformanceStats() map[string]time.Duration {
	return p.performanceStats
}
