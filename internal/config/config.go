package config

import (
	"errors"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"gopkg.in/yaml.v3"
)

// ClickHouseConfig ClickHouse配置 - 与成功程序保持一致
type ClickHouseConfig struct {
	Host           string `yaml:"host"`
	Port           string `yaml:"port"` // 改为字符串类型，与成功程序一致
	Database       string `yaml:"database"`
	Username       string `yaml:"username"`
	Password       string `yaml:"password"`
	ConnectTimeout string `yaml:"connect_timeout"` // 改为字符串类型，便于解析
	MaxConnections int    `yaml:"max_connections"`
	Debug          bool   `yaml:"debug"`
}

// ElasticsearchConfig Elasticsearch配置
type ElasticsearchConfig struct {
	Addresses   []string      `yaml:"addresses"`
	Username    string        `yaml:"username"`
	Password    string        `yaml:"password"`
	IndexPrefix string        `yaml:"index_prefix"`
	Timeout     time.Duration `yaml:"timeout"`
	MaxRetries  int           `yaml:"max_retries"`
	Debug       bool          `yaml:"debug"`
	BatchSize   int           `yaml:"batch_size"`
	SyncTimeout time.Duration `yaml:"sync_timeout"`
	VerifySSL   bool          `yaml:"verify_ssl"`
	CACertPath  string        `yaml:"ca_cert_path"`
}

// ComponentConfig 组件配置
type ComponentConfig struct {
	WorkloadName       string   `yaml:"workload_name"`
	ComponentType      string   `yaml:"component_type"`
	DeploymentLocation string   `yaml:"deployment_location"`
	DataSource         string   `yaml:"data_source"`
	Description        string   `yaml:"description"`
	Enabled            bool     `yaml:"enabled"`
	Priority           int      `yaml:"priority"`
	FuzzySearch        bool     `yaml:"fuzzy_search"`
	FuzzySearchField   string   `yaml:"fuzzy_search_field"`   // 新增：指定模糊查询的字段 (name/ownerApp)
	FuzzySearchPattern string   `yaml:"fuzzy_search_pattern"` // 新增：自定义模糊查询模式
	DisabledInspectors []string `yaml:"disabled_inspectors"`
}

// MySQLConfig MySQL配置
type MySQLConfig struct {
	Host           string `yaml:"host"`
	Port           int    `yaml:"port"`
	Database       string `yaml:"database"`
	Username       string `yaml:"username"`
	Password       string `yaml:"password"`
	ConnectTimeout string `yaml:"connect_timeout"`
	MaxConnections int    `yaml:"max_connections"`
	Debug          bool   `yaml:"debug"`
}

// InspectorConfig 检查器配置
type InspectorConfig struct {
	Enabled     bool          `yaml:"enabled"`
	Name        string        `yaml:"name"`
	Description string        `yaml:"description"`
	Timeout     time.Duration `yaml:"timeout"`
}

// ScanConfig 扫描配置
type ScanConfig struct {
	DateRange map[string]interface{} `yaml:"date_range"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level         string `yaml:"level"`
	Format        string `yaml:"format"`
	FilePath      string `yaml:"file_path"`
	MaxFileSize   string `yaml:"max_file_size"`
	BackupCount   int    `yaml:"backup_count"`
	ConsoleOutput bool   `yaml:"console_output"`
}

// OutputConfig 输出配置
type OutputConfig struct {
	Formats       []string               `yaml:"formats"`
	Console       map[string]interface{} `yaml:"console"`
	JSON          map[string]interface{} `yaml:"json"`
	YAML          map[string]interface{} `yaml:"yaml"`
	Elasticsearch map[string]interface{} `yaml:"elasticsearch"`
}

// SystemConfig 系统配置
type SystemConfig struct {
	WorkDir string `yaml:"work_dir"`
	TempDir string `yaml:"temp_dir"`
}

// Config 应用配置
type Config struct {
	ClickHouse            ClickHouseConfig           `yaml:"clickhouse"`
	MySQL                 MySQLConfig                `yaml:"mysql"`
	Elasticsearch         ElasticsearchConfig        `yaml:"elasticsearch"`
	UserClusterComponents map[string]ComponentConfig `yaml:"user_cluster_components"`
	MetaClusterComponents map[string]ComponentConfig `yaml:"meta_cluster_components"`
	Inspectors            map[string]InspectorConfig `yaml:"inspectors"`
	Scan                  ScanConfig                 `yaml:"scan"`
	Logging               LoggingConfig              `yaml:"logging"`
	Output                OutputConfig               `yaml:"output"`
	System                SystemConfig               `yaml:"system"`

	// 保持向后兼容的旧字段
	Components map[string]ComponentConfig `yaml:"components"`
}

// GetEnabledInspectors 获取启用的检查器配置
func (cfg *Config) GetEnabledInspectors() map[string]*InspectorConfig {
	enabled := make(map[string]*InspectorConfig)
	for id, inspector := range cfg.Inspectors {
		if inspector.Enabled {
			enabled[id] = &inspector
		}
	}
	return enabled
}

type ConfigManager struct {
	configPath string
	config     *Config
	mu         sync.RWMutex
}

var (
	configManager *ConfigManager
	once          sync.Once
)

func NewConfigManager(configPath string) *ConfigManager {
	return &ConfigManager{
		configPath: configPath,
	}
}

func GetConfigManager(configPath string) *ConfigManager {
	once.Do(func() {
		configManager = NewConfigManager(configPath)
	})
	return configManager
}

func LoadConfig(configPath string) (*Config, error) {
	manager := GetConfigManager(configPath)
	return manager.LoadConfig()
}

func (c *ConfigManager) LoadConfig() (*Config, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.config != nil {
		return c.config, nil
	}

	configFile := c.configPath
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件 %s 不存在", configFile)
	}

	file, err := os.Open(configFile)
	if err != nil {
		return nil, fmt.Errorf("打开配置文件失败: %w", err)
	}
	defer file.Close()

	var cfg Config
	decoder := yaml.NewDecoder(file)
	if err := decoder.Decode(&cfg); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	c.setDefaults(&cfg)

	if err := c.validateConfig(&cfg); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	c.config = &cfg
	return c.config, nil
}

func (c *ConfigManager) setDefaults(cfg *Config) {
	// ClickHouse默认值
	if cfg.ClickHouse.Host == "" {
		cfg.ClickHouse.Host = "localhost"
	}
	if cfg.ClickHouse.Port == "" {
		cfg.ClickHouse.Port = "9000"
	}
	if cfg.ClickHouse.Database == "" {
		cfg.ClickHouse.Database = "default"
	}
	if cfg.ClickHouse.Username == "" {
		cfg.ClickHouse.Username = "default"
	}
	if cfg.ClickHouse.ConnectTimeout == "" {
		cfg.ClickHouse.ConnectTimeout = "30s"
	}
	if cfg.ClickHouse.MaxConnections == 0 {
		cfg.ClickHouse.MaxConnections = 10
	}

	// MySQL默认值
	if cfg.MySQL.Host == "" {
		cfg.MySQL.Host = "localhost"
	}
	if cfg.MySQL.Port == 0 {
		cfg.MySQL.Port = 3306
	}
	if cfg.MySQL.Database == "" {
		cfg.MySQL.Database = "cmdb"
	}
	if cfg.MySQL.Username == "" {
		cfg.MySQL.Username = "root"
	}
	if cfg.MySQL.ConnectTimeout == "" {
		cfg.MySQL.ConnectTimeout = "30s"
	}
	if cfg.MySQL.MaxConnections == 0 {
		cfg.MySQL.MaxConnections = 10
	}

	// Elasticsearch默认值
	if cfg.Elasticsearch.IndexPrefix == "" {
		cfg.Elasticsearch.IndexPrefix = "cmdb"
	}
	if cfg.Elasticsearch.Timeout == 0 {
		cfg.Elasticsearch.Timeout = 30 * time.Second
	}
	if cfg.Elasticsearch.MaxRetries == 0 {
		cfg.Elasticsearch.MaxRetries = 3
	}
	if cfg.Elasticsearch.BatchSize == 0 {
		cfg.Elasticsearch.BatchSize = 1000
	}
	if cfg.Elasticsearch.SyncTimeout == 0 {
		cfg.Elasticsearch.SyncTimeout = 300 * time.Second
	}

	// 日志默认值
	if cfg.Logging.Level == "" {
		cfg.Logging.Level = "INFO"
	}
	if cfg.Logging.Format == "" {
		cfg.Logging.Format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
	}
	if cfg.Logging.FilePath == "" {
		cfg.Logging.FilePath = "logs/cmdb_scanner.log"
	}
	if cfg.Logging.MaxFileSize == "" {
		cfg.Logging.MaxFileSize = "100MB"
	}
	if cfg.Logging.BackupCount == 0 {
		cfg.Logging.BackupCount = 5
	}

	// 系统默认值
	if cfg.System.WorkDir == "" {
		cfg.System.WorkDir = "./work"
	}
	if cfg.System.TempDir == "" {
		cfg.System.TempDir = "./temp"
	}

	// 输出默认值
	if len(cfg.Output.Formats) == 0 {
		cfg.Output.Formats = []string{"console"}
	}

	// 向后兼容：如果使用旧的components配置，迁移到新结构
	if len(cfg.Components) > 0 && len(cfg.UserClusterComponents) == 0 {
		cfg.UserClusterComponents = make(map[string]ComponentConfig)
		for name, comp := range cfg.Components {
			// 为旧配置设置默认值
			if comp.DeploymentLocation == "" {
				comp.DeploymentLocation = "用户集群"
			}
			if comp.DataSource == "" {
				comp.DataSource = "clickhouse"
			}
			if comp.DisabledInspectors == nil {
				comp.DisabledInspectors = []string{}
			}
			cfg.UserClusterComponents[name] = comp
		}
	}

	// 确保新配置字段有默认值
	for name, comp := range cfg.UserClusterComponents {
		if comp.DisabledInspectors == nil {
			comp.DisabledInspectors = []string{}
			cfg.UserClusterComponents[name] = comp
		}
	}
	for name, comp := range cfg.MetaClusterComponents {
		if comp.DisabledInspectors == nil {
			comp.DisabledInspectors = []string{}
			cfg.MetaClusterComponents[name] = comp
		}
	}
}

func (c *ConfigManager) validateConfig(cfg *Config) error {
	// ClickHouse配置验证
	if cfg.ClickHouse.Host == "" {
		return errors.New("ClickHouse host不能为空")
	}
	if cfg.ClickHouse.Port == "" {
		return errors.New("ClickHouse port不能为空")
	}

	// Elasticsearch配置验证
	if len(cfg.Elasticsearch.Addresses) > 0 {
		for _, addr := range cfg.Elasticsearch.Addresses {
			if !strings.HasPrefix(addr, "http://") && !strings.HasPrefix(addr, "https://") {
				return errors.New("Elasticsearch地址必须以http://或https://开头")
			}
		}
	}

	// 组件配置验证
	totalComponents := len(cfg.UserClusterComponents) + len(cfg.MetaClusterComponents) + len(cfg.Components)
	if totalComponents == 0 {
		return errors.New("未配置任何扫描组件")
	}

	// 验证用户集群组件
	for compName, compConfig := range cfg.UserClusterComponents {
		if err := c.validateComponent(compName, compConfig, "user"); err != nil {
			return fmt.Errorf("用户集群组件 %s 配置错误: %w", compName, err)
		}
	}

	// 验证Meta集群组件
	for compName, compConfig := range cfg.MetaClusterComponents {
		if err := c.validateComponent(compName, compConfig, "meta"); err != nil {
			return fmt.Errorf("Meta集群组件 %s 配置错误: %w", compName, err)
		}
	}

	// 向后兼容：验证旧的components配置
	for compName, compConfig := range cfg.Components {
		if compConfig.WorkloadName == "" {
			return fmt.Errorf("组件 %s 的workload_name不能为空", compName)
		}
	}

	// 验证检查器配置
	if err := c.validateInspectors(cfg); err != nil {
		return err
	}

	return nil
}

func (c *ConfigManager) validateComponent(compName string, compConfig ComponentConfig, expectedType string) error {
	// 验证必需字段
	if compConfig.WorkloadName == "" {
		return fmt.Errorf("workload_name不能为空")
	}

	// 验证component_type与外层分类的一致性
	if compConfig.ComponentType != expectedType {
		return fmt.Errorf("component_type '%s' 与外层分类 '%s' 不匹配", compConfig.ComponentType, expectedType)
	}

	// 验证data_source与component_type的对应关系
	expectedDataSource := "clickhouse"
	if expectedType == "meta" {
		expectedDataSource = "mysql"
	}
	if compConfig.DataSource != "" && compConfig.DataSource != expectedDataSource {
		return fmt.Errorf("component_type '%s' 应该使用 '%s' 数据源，但配置为 '%s'",
			expectedType, expectedDataSource, compConfig.DataSource)
	}

	return nil
}

func (c *ConfigManager) validateInspectors(cfg *Config) error {
	// 收集所有可用的检查器ID
	availableInspectors := make(map[string]bool)
	for inspectorID := range cfg.Inspectors {
		availableInspectors[inspectorID] = true
	}

	// 验证用户集群组件的disabled_inspectors
	for compName, compConfig := range cfg.UserClusterComponents {
		for _, disabledInspector := range compConfig.DisabledInspectors {
			if !availableInspectors[disabledInspector] {
				return fmt.Errorf("用户集群组件 %s 的 disabled_inspectors 中包含未知检查器: %s",
					compName, disabledInspector)
			}
		}
	}

	// 验证Meta集群组件的disabled_inspectors
	for compName, compConfig := range cfg.MetaClusterComponents {
		for _, disabledInspector := range compConfig.DisabledInspectors {
			if !availableInspectors[disabledInspector] {
				return fmt.Errorf("Meta集群组件 %s 的 disabled_inspectors 中包含未知检查器: %s",
					compName, disabledInspector)
			}
		}
	}

	return nil
}

func (c *ConfigManager) ReloadConfig() (*Config, error) {
	c.mu.Lock()
	c.config = nil
	c.mu.Unlock()
	return c.LoadConfig()
}
