package region

import (
	"strings"
)

type RegionData struct {
	Code        string `json:"code"`
	CountryCode string `json:"country_code"`
	<PERSON><PERSON>       string `json:"alias"`
	ZoneNum     int    `json:"zone_num"`
}

var REGION_DATA = []RegionData{
	{"ap-guangzhou", "CN", "gz", 8},
	{"ap-shanghai", "CN", "sh", 7},
	{"ap-hongkong", "HK", "hk", 3},
	{"na-toronto", "CA", "ca", 1},
	{"ap-shanghai-fsi", "CN", "shjr", 3},
	{"ap-beijing", "CN", "bj", 7},
	{"ap-singapore", "SG", "sg", 4},
	{"ap-shenzhen-fsi", "CN", "szjr", 3},
	{"na-siliconvalley", "US", "usw", 2},
	{"ap-chengdu", "CN", "cd", 2},
	{"eu-frankfurt", "DE", "de", 2},
	{"ap-seoul", "KR", "kr", 2},
	{"ap-chongqing", "CN", "cq", 1},
	{"ap-mumbai", "IN", "in", 2},
	{"na-ashburn", "US", "use", 2},
	{"ap-bangkok", "TH", "th", 2},
	{"ap-tokyo", "JP", "jp", 2},
	{"ap-jinan-ec", "CN", "jnec", 1},
	{"ap-hangzhou-ec", "CN", "hzec", 1},
	{"ap-nanjing", "CN", "nj", 3},
	{"ap-fuzhou-ec", "CN", "fzec", 1},
	{"ap-wuhan-ec", "CN", "whec", 1},
	{"ap-tianjin", "CN", "tsn", 3},
	{"ap-shenzhen", "CN", "szx", 4},
	{"ap-taipei", "TW", "tpe", 1},
	{"ap-changsha-ec", "CN", "csec", 1},
	{"ap-beijing-fsi", "CN", "bjjr", 2},
	{"ap-shijiazhuang-ec", "CN", "sjwec", 1},
	{"ap-qingyuan", "CN", "qy", 1},
	{"ap-hefei-ec", "CN", "hfeec", 1},
	{"ap-shenyang-ec", "CN", "sheec", 1},
	{"ap-xian-ec", "CN", "xiyec", 1},
	{"ap-xibei-ec", "CN", "xbec", 1},
	{"ap-jakarta", "ID", "jkt", 3},
	{"ap-qingyuan-xinan", "CN", "qyxa", 1},
	{"sa-saopaulo", "BR", "sao", 1},
	{"ap-shanghai-adc", "CN", "shadc", 3},
	{"ap-guangzhou-wxzf", "CN", "gzwxzf", 3},
	{"ap-shanghai-wxzf", "CN", "shwxzf", 3},
	{"ap-shenzhen-jxcft", "CN", "szjxcft", 1},
	{"ap-shanghai-hq-cft", "CN", "shhqcft", 1},
	{"ap-shanghai-hq-uat-cft", "CN", "shhqcftfzhj", 1},
	{"ap-shanghai-wxp-ops", "CN", "shwxzfjpyzc", 3},
}

func GetRegionByCode(code string) *RegionData {
	for _, region := range REGION_DATA {
		if region.Code == code {
			return &region
		}
	}
	return nil
}

func GetZoneNumByAlias(alias string) int {
	for _, region := range REGION_DATA {
		if region.Alias == alias {
			return region.ZoneNum
		}
	}
	return 0
}

func GetZoneNumByCode(code string) int {
	region := GetRegionByCode(code)
	if region != nil {
		return region.ZoneNum
	}
	return 0
}

func GetRegionsByCountry(countryCode string) []RegionData {
	var regions []RegionData
	for _, region := range REGION_DATA {
		if region.CountryCode == countryCode {
			regions = append(regions, region)
		}
	}
	return regions
}

func GetRegionsByZone(zoneNum int) []RegionData {
	var regions []RegionData
	for _, region := range REGION_DATA {
		if region.ZoneNum == zoneNum {
			regions = append(regions, region)
		}
	}
	return regions
}

func GetZoneNumBySuffixMatch(regionInput string) int {
	if regionInput == "" {
		return 0
	}

	regionInputLower := strings.ToLower(regionInput)

	for _, region := range REGION_DATA {
		codeLower := strings.ToLower(region.Code)
		if strings.HasSuffix(codeLower, regionInputLower) || strings.Contains(codeLower, regionInputLower) {
			return region.ZoneNum
		}
	}
	return 0
}

func GetZoneNumWithFallback(regionInput string) int {
	if regionInput == "" {
		return 0
	}

	// 第一层：尝试通过alias查询
	zoneNum := GetZoneNumByAlias(regionInput)
	if zoneNum > 0 {
		return zoneNum
	}

	// 第二层：尝试通过code查询
	zoneNum = GetZoneNumByCode(regionInput)
	if zoneNum > 0 {
		return zoneNum
	}

	// 第三层：尝试通过后缀匹配查询
	zoneNum = GetZoneNumBySuffixMatch(regionInput)
	if zoneNum > 0 {
		return zoneNum
	}

	return 0
}

func GetRegionAlias(regionCode string) string {
	region := GetRegionByCode(regionCode)
	if region != nil {
		return region.Alias
	}
	return regionCode
}

func GetAllRegions() []RegionData {
	return REGION_DATA
}

func GetRegionCount() int {
	return len(REGION_DATA)
}

func GetCountryCodes() []string {
	countryMap := make(map[string]bool)
	for _, region := range REGION_DATA {
		countryMap[region.CountryCode] = true
	}

	var countries []string
	for country := range countryMap {
		countries = append(countries, country)
	}
	return countries
}
