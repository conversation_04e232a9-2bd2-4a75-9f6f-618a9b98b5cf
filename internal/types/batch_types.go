package types

import "time"

// ClusterMetadata 集群基础信息
type ClusterMetadata struct {
	ClusterID   string `json:"cluster_id"`
	ClusterName string `json:"cluster_name"`
	ClusterType string `json:"cluster_type"`
	Region      string `json:"region"`
	AppID       uint64 `json:"app_id"`
	Version     string `json:"version"`
	Status      string `json:"status"`
	DataSource  string `json:"data_source"`
}

// ClusterBatchTask 批处理任务
type ClusterBatchTask struct {
	ClusterIDs []string
	BatchID    int
}

// BatchResult 批处理结果
type BatchResult struct {
	BatchID   int                       `json:"batch_id"`
	Results   map[string]*ClusterResult `json:"results"`
	Error     error                     `json:"error,omitempty"`
	Timestamp time.Time                 `json:"timestamp"`
}

// ClusterResult 单个集群的检查结果
type ClusterResult struct {
	ClusterID        string                      `json:"cluster_id"`
	ClusterMetadata  ClusterMetadata             `json:"cluster_metadata"`
	ComponentResults map[string]*ComponentResult `json:"component_results"`
}

// ComponentResult 组件检查结果
type ComponentResult struct {
	ComponentName     string                      `json:"component_name"`
	ComponentType     string                      `json:"component_type"` // user_cluster, meta_cluster
	InspectionResults map[string]InspectionResult `json:"inspection_results"`
}

// InspectionResult 检查结果
type InspectionResult struct {
	InspectorID   string                 `json:"inspector_id"`
	InspectorName string                 `json:"inspector_name"`
	Status        string                 `json:"status"` // passed, failed, unknown, no_pods
	Message       string                 `json:"message"`
	Details       map[string]interface{} `json:"details"`
}

// PodDataBatch Pod数据批处理结果
type PodDataBatch struct {
	ClusterPodData map[string]*ClusterPodInfo // cluster_id -> pod info
}

// ClusterPodInfo 单个集群的Pod信息
type ClusterPodInfo struct {
	ClusterID        string   `json:"cluster_id"`
	NodeNames        []string `json:"node_names"`
	TotalRunningPods int      `json:"total_running_pods"`
}

// GetAllNodeNames 获取所有节点名称
func (p *PodDataBatch) GetAllNodeNames() []string {
	nodeSet := make(map[string]bool)
	for _, podInfo := range p.ClusterPodData {
		for _, nodeName := range podInfo.NodeNames {
			if nodeName != "" {
				nodeSet[nodeName] = true
			}
		}
	}

	var allNodes []string
	for nodeName := range nodeSet {
		allNodes = append(allNodes, nodeName)
	}
	return allNodes
}

// NodeLabels 节点标签信息
type NodeLabels struct {
	NodeName string                 `json:"node_name"`
	Labels   map[string]interface{} `json:"labels"`
	Zone     string                 `json:"zone"`
	Region   string                 `json:"region"`
}

// MetaComponentDataBatch Meta组件数据批处理结果（空实现）
type MetaComponentDataBatch struct {
	ClusterComponentData map[string]*MetaClusterComponentInfo
}

// MetaClusterComponentInfo Meta集群组件信息
type MetaClusterComponentInfo struct {
	ClusterID     string   `json:"cluster_id"`
	ComponentName string   `json:"component_name"`
	Region        string   `json:"region"`
	ClusterType   string   `json:"cluster_type"` // tke/eks等
	ZoneCount     int      `json:"zone_count"`
	PodCount      int      `json:"pod_count"`
	Zones         []string `json:"zones"` // 新增：具体的zone列表
	Status        string   `json:"status"`
}

// MetaNodeInfo Meta节点信息（空实现）
type MetaNodeInfo struct {
	NodeName string `json:"node_name"`
	Zone     string `json:"zone"`
	Region   string `json:"region"`
}

// InspectorInfo 检查器信息
type InspectorInfo struct {
	ID          string        `json:"id"`
	Name        string        `json:"name"`
	Description string        `json:"description"`
	Timeout     time.Duration `json:"timeout"`
}
