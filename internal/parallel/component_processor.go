package parallel

import (
	"context"
	"fmt"
	"sync"
	"time"

	"cmdb-scanner/internal/cache"
	"cmdb-scanner/internal/config"
	"cmdb-scanner/internal/database"
	"cmdb-scanner/internal/inspectors"
	"cmdb-scanner/internal/types"

	"github.com/sirupsen/logrus"
)

// ComponentProcessor 并行组件处理器
type ComponentProcessor struct {
	clusterIDs       []string
	batchID          int
	clickhouseClient *database.ClickHouseClient
	mysqlClient      *database.MySQLClient
	config           *config.Config
	scanDate         time.Time
	logger           *logrus.Logger
	cacheManager     *cache.DataCacheManager

	// 并发控制
	maxConcurrency int
	semaphore      chan struct{}
}

// NewComponentProcessor 创建并行组件处理器
func NewComponentProcessor(
	clusterIDs []string,
	batchID int,
	clickhouseClient *database.ClickHouseClient,
	mysqlClient *database.MySQLClient,
	config *config.Config,
	scanDate time.Time,
	logger *logrus.Logger,
	cacheManager *cache.DataCacheManager,
) *ComponentProcessor {
	maxConcurrency := 10 // 默认最大并发数
	if len(clusterIDs) < maxConcurrency {
		maxConcurrency = len(clusterIDs)
	}

	return &ComponentProcessor{
		clusterIDs:       clusterIDs,
		batchID:          batchID,
		clickhouseClient: clickhouseClient,
		mysqlClient:      mysqlClient,
		config:           config,
		scanDate:         scanDate,
		logger:           logger,
		cacheManager:     cacheManager,
		maxConcurrency:   maxConcurrency,
		semaphore:        make(chan struct{}, maxConcurrency),
	}
}

// ProcessUserComponentsParallel 并行处理用户组件
func (p *ComponentProcessor) ProcessUserComponentsParallel() (map[string]map[string]map[string]types.InspectionResult, error) {
	p.logger.Infof("🚀 [ComponentProcessor] 开始并行处理用户组件，批次: %d", p.batchID)
	startTime := time.Now()

	// 获取所有启用的用户组件
	var enabledComponents []config.ComponentConfig
	var componentNames []string
	
	for componentName, component := range p.config.UserClusterComponents {
		if component.Enabled {
			component.WorkloadName = componentName // 确保组件名称正确
			enabledComponents = append(enabledComponents, component)
			componentNames = append(componentNames, componentName)
		}
	}

	if len(enabledComponents) == 0 {
		p.logger.Infof("📋 [ComponentProcessor] 没有启用的用户组件")
		return make(map[string]map[string]map[string]types.InspectionResult), nil
	}

	p.logger.Infof("📊 [ComponentProcessor] 用户组件数量: %d, 组件列表: %v", len(enabledComponents), componentNames)

	// 结果收集
	results := make(map[string]map[string]map[string]types.InspectionResult)
	var resultsMutex sync.Mutex

	// 并行处理组件
	var wg sync.WaitGroup
	errChan := make(chan error, len(enabledComponents))

	for i, component := range enabledComponents {
		wg.Add(1)
		go func(componentName string, comp config.ComponentConfig, index int) {
			defer wg.Done()

			// 获取信号量
			p.semaphore <- struct{}{}
			defer func() { <-p.semaphore }()

			p.logger.Infof("🔍 [ComponentProcessor] 处理用户组件 %d/%d: %s", index+1, len(enabledComponents), componentName)
			
			componentResults, err := p.processUserComponent(componentName, comp)
			if err != nil {
				errChan <- fmt.Errorf("处理用户组件 %s 失败: %w", componentName, err)
				return
			}

			// 合并结果
			resultsMutex.Lock()
			for clusterID, inspectorResults := range componentResults {
				if results[clusterID] == nil {
					results[clusterID] = make(map[string]map[string]types.InspectionResult)
				}
				results[clusterID][componentName] = inspectorResults
			}
			resultsMutex.Unlock()

			p.logger.Infof("✅ [ComponentProcessor] 用户组件 %s 处理完成", componentName)
		}(componentNames[i], component, i)
	}

	wg.Wait()
	close(errChan)

	// 检查错误
	for err := range errChan {
		if err != nil {
			return nil, err
		}
	}

	duration := time.Since(startTime)
	p.logger.Infof("✅ [ComponentProcessor] 用户组件并行处理完成，耗时: %v", duration)

	return results, nil
}

// ProcessMetaComponentsParallel 并行处理Meta组件
func (p *ComponentProcessor) ProcessMetaComponentsParallel() (map[string]map[string]map[string]types.InspectionResult, error) {
	p.logger.Infof("🚀 [ComponentProcessor] 开始并行处理Meta组件，批次: %d", p.batchID)
	startTime := time.Now()

	// 获取所有启用的Meta组件
	var enabledComponents []config.ComponentConfig
	var componentNames []string
	
	for componentName, component := range p.config.MetaClusterComponents {
		if component.Enabled {
			component.WorkloadName = componentName // 确保组件名称正确
			enabledComponents = append(enabledComponents, component)
			componentNames = append(componentNames, componentName)
		}
	}

	if len(enabledComponents) == 0 {
		p.logger.Infof("📋 [ComponentProcessor] 没有启用的Meta组件")
		return make(map[string]map[string]map[string]types.InspectionResult), nil
	}

	p.logger.Infof("📊 [ComponentProcessor] Meta组件数量: %d, 组件列表: %v", len(enabledComponents), componentNames)

	// 结果收集
	results := make(map[string]map[string]map[string]types.InspectionResult)
	var resultsMutex sync.Mutex

	// 并行处理组件
	var wg sync.WaitGroup
	errChan := make(chan error, len(enabledComponents))

	for i, component := range enabledComponents {
		wg.Add(1)
		go func(componentName string, comp config.ComponentConfig, index int) {
			defer wg.Done()

			// 获取信号量
			p.semaphore <- struct{}{}
			defer func() { <-p.semaphore }()

			p.logger.Infof("🔍 [ComponentProcessor] 处理Meta组件 %d/%d: %s", index+1, len(enabledComponents), componentName)
			
			componentResults, err := p.processMetaComponent(componentName, comp)
			if err != nil {
				errChan <- fmt.Errorf("处理Meta组件 %s 失败: %w", componentName, err)
				return
			}

			// 合并结果
			resultsMutex.Lock()
			for clusterID, inspectorResults := range componentResults {
				if results[clusterID] == nil {
					results[clusterID] = make(map[string]map[string]types.InspectionResult)
				}
				results[clusterID][componentName] = inspectorResults
			}
			resultsMutex.Unlock()

			p.logger.Infof("✅ [ComponentProcessor] Meta组件 %s 处理完成", componentName)
		}(componentNames[i], component, i)
	}

	wg.Wait()
	close(errChan)

	// 检查错误
	for err := range errChan {
		if err != nil {
			return nil, err
		}
	}

	duration := time.Since(startTime)
	p.logger.Infof("✅ [ComponentProcessor] Meta组件并行处理完成，耗时: %v", duration)

	return results, nil
}

// processUserComponent 处理单个用户组件
func (p *ComponentProcessor) processUserComponent(componentName string, component config.ComponentConfig) (map[string]map[string]types.InspectionResult, error) {
	// 结果结构：clusterID -> inspectorID -> result
	results := make(map[string]map[string]types.InspectionResult)

	// 并行处理检查器
	return p.processInspectorsParallel(componentName, component, "user", results)
}

// processMetaComponent 处理单个Meta组件
func (p *ComponentProcessor) processMetaComponent(componentName string, component config.ComponentConfig) (map[string]map[string]types.InspectionResult, error) {
	// 结果结构：clusterID -> inspectorID -> result
	results := make(map[string]map[string]types.InspectionResult)

	// 并行处理检查器
	return p.processInspectorsParallel(componentName, component, "meta", results)
}

// processInspectorsParallel 并行处理检查器
func (p *ComponentProcessor) processInspectorsParallel(
	componentName string,
	component config.ComponentConfig,
	componentType string,
	results map[string]map[string]types.InspectionResult,
) (map[string]map[string]types.InspectionResult, error) {
	
	// 获取启用的检查器
	var enabledInspectors []string
	for inspectorID, inspectorConfig := range p.config.Inspectors {
		if !inspectorConfig.Enabled {
			continue
		}
		if p.isInspectorDisabled(component.DisabledInspectors, inspectorID) {
			continue
		}
		enabledInspectors = append(enabledInspectors, inspectorID)
	}

	if len(enabledInspectors) == 0 {
		p.logger.Debugf("📋 [ComponentProcessor] 组件 %s 没有启用的检查器", componentName)
		return results, nil
	}

	var resultsMutex sync.Mutex
	var wg sync.WaitGroup
	errChan := make(chan error, len(enabledInspectors))

	// 并行执行检查器
	for _, inspectorID := range enabledInspectors {
		wg.Add(1)
		go func(inspID string) {
			defer wg.Done()

			var inspector interface{}
			var err error

			// 创建检查器
			if componentType == "user" {
				inspector, err = p.createUserClusterInspector(inspID)
			} else {
				inspector, err = p.createMetaClusterInspector(inspID)
			}

			if err != nil {
				errChan <- fmt.Errorf("创建检查器 %s 失败: %w", inspID, err)
				return
			}

			// 执行检查
			var inspectorResults map[string]types.InspectionResult
			if userInspector, ok := inspector.(interface {
				InspectComponentBatch(context.Context, []string, string) (map[string]types.InspectionResult, error)
			}); ok {
				inspectorResults, err = userInspector.InspectComponentBatch(context.TODO(), p.clusterIDs, componentName)
			} else {
				errChan <- fmt.Errorf("检查器 %s 类型不匹配", inspID)
				return
			}

			if err != nil {
				errChan <- fmt.Errorf("检查器 %s 执行失败: %w", inspID, err)
				return
			}

			// 合并结果
			resultsMutex.Lock()
			for clusterID, result := range inspectorResults {
				if results[clusterID] == nil {
					results[clusterID] = make(map[string]types.InspectionResult)
				}
				results[clusterID][inspID] = result
			}
			resultsMutex.Unlock()

		}(inspectorID)
	}

	wg.Wait()
	close(errChan)

	// 检查错误
	for err := range errChan {
		if err != nil {
			return nil, err
		}
	}

	return results, nil
}

// isInspectorDisabled 检查检查器是否被禁用
func (p *ComponentProcessor) isInspectorDisabled(disabledInspectors []string, inspectorID string) bool {
	for _, disabled := range disabledInspectors {
		if disabled == inspectorID {
			return true
		}
	}
	return false
}

// createUserClusterInspector 创建用户集群检查器
func (p *ComponentProcessor) createUserClusterInspector(inspectorID string) (interface{}, error) {
	switch inspectorID {
	case "multi_az_distribution":
		return inspectors.NewUserClusterBatchMultiAZInspectorOptimized(
			p.clickhouseClient, p.config, p.scanDate, p.logger, p.cacheManager,
		), nil
	default:
		return nil, fmt.Errorf("未知的用户集群检查器: %s", inspectorID)
	}
}

// createMetaClusterInspector 创建Meta集群检查器
func (p *ComponentProcessor) createMetaClusterInspector(inspectorID string) (interface{}, error) {
	switch inspectorID {
	case "multi_az_distribution":
		return inspectors.NewMetaClusterBatchMultiAZInspector(
			p.mysqlClient, p.config, p.scanDate, p.logger, p.cacheManager,
		), nil
	default:
		return nil, fmt.Errorf("未知的Meta集群检查器: %s", inspectorID)
	}
}
