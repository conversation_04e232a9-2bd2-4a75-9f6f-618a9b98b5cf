# CMDB Scanner 优化构建配置

# 基本信息
APP_NAME := cmdb-scanner
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建参数
CGO_ENABLED := 0
GOOS := linux
GOARCH := amd64

# 链接器标志
LDFLAGS := -s -w \
	-X 'main.Version=$(VERSION)' \
	-X 'main.BuildTime=$(BUILD_TIME)' \
	-X 'main.GitCommit=$(GIT_COMMIT)' \
	-extldflags "-static"

# 构建标志
BUILD_FLAGS := -a -installsuffix cgo -trimpath

# 默认目标
.PHONY: all
all: build

# 标准构建
.PHONY: build
build:
	@echo "🔨 构建 $(APP_NAME)..."
	CGO_ENABLED=$(CGO_ENABLED) GOOS=$(GOOS) GOARCH=$(GOARCH) \
	go build $(BUILD_FLAGS) -ldflags '$(LDFLAGS)' -o $(APP_NAME) ./cmd
	@echo "✅ 构建完成: $(APP_NAME)"
	@ls -lh $(APP_NAME)

# 生产环境构建（你当前使用的方式）
.PHONY: build-prod
build-prod:
	@echo "🚀 生产环境构建 $(APP_NAME)..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 \
	go build -a -ldflags '-extldflags "-static" -s -w' -o cs65 ./cmd
	@echo "✅ 生产构建完成: cs65"
	@ls -lh cs65
	@file cs65

# 带版本信息的构建
.PHONY: build-versioned
build-versioned:
	@echo "📦 带版本信息构建 $(APP_NAME)..."
	CGO_ENABLED=$(CGO_ENABLED) GOOS=$(GOOS) GOARCH=$(GOARCH) \
	go build $(BUILD_FLAGS) -ldflags '$(LDFLAGS)' -o $(APP_NAME)-$(VERSION) ./cmd
	@echo "✅ 版本构建完成: $(APP_NAME)-$(VERSION)"
	@ls -lh $(APP_NAME)-$(VERSION)

# 多平台构建
.PHONY: build-all
build-all:
	@echo "🌍 多平台构建..."
	# Linux AMD64
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 \
	go build $(BUILD_FLAGS) -ldflags '$(LDFLAGS)' -o $(APP_NAME)-linux-amd64 ./cmd
	# Linux ARM64
	CGO_ENABLED=0 GOOS=linux GOARCH=arm64 \
	go build $(BUILD_FLAGS) -ldflags '$(LDFLAGS)' -o $(APP_NAME)-linux-arm64 ./cmd
	# Darwin AMD64
	CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 \
	go build $(BUILD_FLAGS) -ldflags '$(LDFLAGS)' -o $(APP_NAME)-darwin-amd64 ./cmd
	# Darwin ARM64
	CGO_ENABLED=0 GOOS=darwin GOARCH=arm64 \
	go build $(BUILD_FLAGS) -ldflags '$(LDFLAGS)' -o $(APP_NAME)-darwin-arm64 ./cmd
	@echo "✅ 多平台构建完成"
	@ls -lh $(APP_NAME)-*

# 清理
.PHONY: clean
clean:
	@echo "🧹 清理构建文件..."
	rm -f $(APP_NAME) $(APP_NAME)-* cs65
	@echo "✅ 清理完成"

# 测试
.PHONY: test
test:
	@echo "🧪 运行测试..."
	go test -v ./...

# 代码检查
.PHONY: lint
lint:
	@echo "🔍 代码检查..."
	go vet ./...
	go fmt ./...

# 依赖更新
.PHONY: deps
deps:
	@echo "📦 更新依赖..."
	go mod tidy
	go mod download

# 安装
.PHONY: install
install: build
	@echo "📥 安装到系统..."
	sudo cp $(APP_NAME) /usr/local/bin/
	@echo "✅ 安装完成"

# 显示构建信息
.PHONY: info
info:
	@echo "📋 构建信息:"
	@echo "  应用名称: $(APP_NAME)"
	@echo "  版本: $(VERSION)"
	@echo "  构建时间: $(BUILD_TIME)"
	@echo "  Git提交: $(GIT_COMMIT)"
	@echo "  目标系统: $(GOOS)/$(GOARCH)"
	@echo "  CGO: $(CGO_ENABLED)"

# 帮助
.PHONY: help
help:
	@echo "📖 可用命令:"
	@echo "  build         - 标准构建"
	@echo "  build-prod    - 生产环境构建（你当前的方式）"
	@echo "  build-versioned - 带版本信息构建"
	@echo "  build-all     - 多平台构建"
	@echo "  clean         - 清理构建文件"
	@echo "  test          - 运行测试"
	@echo "  lint          - 代码检查"
	@echo "  deps          - 更新依赖"
	@echo "  install       - 安装到系统"
	@echo "  info          - 显示构建信息"
	@echo "  help          - 显示此帮助"